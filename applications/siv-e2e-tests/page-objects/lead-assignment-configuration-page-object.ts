import { Page, Locator, expect } from '@playwright/test';

/**
 * Page object for the Lead Assignment Configuration page
 * Encapsulates common patterns for configuring teams, individuals, and criteria
 */
export class LeadAssignmentConfigurationPageObject {
  readonly page: Page;

  // Main navigation elements
  readonly defineAssignmentCriteriaButton: Locator;
  readonly addTeamButton: Locator;
  readonly addIndividualButton: Locator;
  readonly validateRulesButton: Locator;
  readonly viewAssignmentSummaryButton: Locator;
  readonly saveActivateButton: Locator;

  // Geography management
  readonly geographyHeaderButton: Locator;
  readonly quickAddNorthAmericaButton: Locator;
  readonly quickAddApacButton: Locator;
  readonly quickAddWesternEuropeButton: Locator;
  readonly userDefinedRegions: Locator;
  readonly footerSaveAllChangesButton: Locator;

  // Room count management
  readonly roomCountHeaderButton: Locator;
  readonly bucketNameInput: Locator;
  readonly conditionTypeSelect: Locator;
  readonly minValueInput: Locator;
  readonly maxValueInput: Locator;
  readonly saveBucketButton: Locator;
  readonly saveAllRoomCountBucketsButton: Locator;

  constructor(page: Page) {
    this.page = page;
    
    // Initialize main navigation elements
    this.defineAssignmentCriteriaButton = page.getByRole('button', { name: 'Define Assignment Criteria' });
    this.addTeamButton = page.getByTestId('add-team-button');
    this.addIndividualButton = page.getByTestId('add-individual-button');
    this.validateRulesButton = page.getByRole('button', { name: 'Validate Rules' });
    this.viewAssignmentSummaryButton = page.getByRole('button', { name: 'View Assignment Summary' });
    this.saveActivateButton = page.getByRole('button', { name: 'Save & Activate' });

    // Geography management elements
    this.geographyHeaderButton = page.getByTestId('geography-header').getByRole('button');
    this.quickAddNorthAmericaButton = page.getByTestId('quick-add-template-north-america');
    this.quickAddApacButton = page.getByTestId('quick-add-template-apac');
    this.quickAddWesternEuropeButton = page.getByTestId('quick-add-template-western-europe');
    this.userDefinedRegions = page.getByTestId('user-defined-regions');
    this.footerSaveAllChangesButton = page.getByTestId('footer-save-all-changes-button');

    // Room count management elements
    this.roomCountHeaderButton = page.getByTestId('roomCount-header').getByRole('button');
    this.bucketNameInput = page.getByTestId('bucket-name-input');
    this.conditionTypeSelect = page.getByTestId('condition-type');
    this.minValueInput = page.getByTestId('min-value-input');
    this.maxValueInput = page.getByTestId('max-value-input');
    this.saveBucketButton = page.getByTestId('save-bucket');
    this.saveAllRoomCountBucketsButton = page.getByTestId('save-all-roomcount-buckets');
  }

  /**
   * Navigate to the lead assignment configuration page
   */
  async navigate() {
    await this.page.goto('/app/sales-deployment-onboarding');
    await this.page.waitForLoadState('networkidle');
    await expect(this.page.getByRole('heading', { name: 'Configure Your Lead Assignment Rules' })).toBeVisible();
  }

  /**
   * Define assignment criteria by selecting checkboxes
   */
  async defineAssignmentCriteria(criteria: {
    geography?: boolean;
    roomCount?: boolean;
    eventType?: boolean;
    industry?: boolean;
  }) {
    await this.page.waitForTimeout(1000);
    await expect(this.defineAssignmentCriteriaButton).toBeVisible();
    await expect(this.defineAssignmentCriteriaButton).toBeEnabled();
    await this.defineAssignmentCriteriaButton.click({ force: true });
    
    await expect(this.page.getByRole('dialog')).toBeVisible({ timeout: 10000 });
    const dialog = this.page.getByRole('dialog');
    await expect(dialog.getByRole('heading', { name: 'Select Your Assignment Factors' })).toBeVisible();

    if (criteria.geography) {
      await this.page.getByTestId('geography-checkbox').click();
      await expect(this.page.getByTestId('geography-checkbox')).toHaveAttribute('data-state', 'checked');
    }
    if (criteria.roomCount) {
      await this.page.getByTestId('roomCount-checkbox').click();
      await expect(this.page.getByTestId('roomCount-checkbox')).toHaveAttribute('data-state', 'checked');
    }
    if (criteria.eventType) {
      await this.page.getByTestId('eventType-checkbox').click();
      await expect(this.page.getByTestId('eventType-checkbox')).toHaveAttribute('data-state', 'checked');
    }
    if (criteria.industry) {
      await this.page.getByTestId('industry-checkbox').click();
      await expect(this.page.getByTestId('industry-checkbox')).toHaveAttribute('data-state', 'checked');
    }

    await this.page.getByTestId('save-criteria-button').click();
  }

  /**
   * Configure geography regions using quick-add templates
   */
  async configureGeographyRegions(regions: string[]) {
    await this.geographyHeaderButton.click();
    
    for (const region of regions) {
      switch (region) {
        case 'north-america':
          await this.quickAddNorthAmericaButton.click();
          break;
        case 'apac':
          await this.quickAddApacButton.click();
          break;
        case 'western-europe':
          await this.quickAddWesternEuropeButton.click();
          break;
      }
    }

    // Verify regions are added
    for (const region of regions) {
      const regionName = region === 'north-america' ? 'North America' : 
                        region === 'apac' ? 'APAC' : 'Western Europe';
      await expect(this.userDefinedRegions.getByText(regionName)).toBeVisible();
    }

    await this.footerSaveAllChangesButton.click();
  }

  /**
   * Configure room count buckets
   */
  async configureRoomCountBuckets(buckets: Array<{
    name: string;
    type: 'less' | 'range' | 'greater';
    minValue?: string;
    maxValue?: string;
  }>) {
    await this.roomCountHeaderButton.click();
    await expect(this.page.getByRole('dialog')).toBeVisible();

    for (const bucket of buckets) {
      await this.bucketNameInput.fill(bucket.name);
      await this.conditionTypeSelect.click();
      await this.page.getByTestId(bucket.type).click();
      
      if (bucket.minValue) {
        await this.minValueInput.fill(bucket.minValue);
      }
      if (bucket.maxValue) {
        await this.maxValueInput.fill(bucket.maxValue);
      }
      
      await this.saveBucketButton.click();
    }

    await this.saveAllRoomCountBucketsButton.click();
    await expect(this.page.getByRole('dialog')).not.toBeVisible();
  }

  /**
   * Create a new team
   */
  async createTeam(teamName: string) {
    await this.addTeamButton.click();
    await expect(this.page.getByRole('heading', { name: 'Add New Team' })).toBeVisible();
    
    await this.page.getByTestId('team-name-input').fill(teamName);
    await this.page.getByTestId('team-save-button').click();
    
    await expect(this.page.getByRole('dialog')).not.toBeVisible();
    await expect(this.page.getByText(teamName)).toBeVisible();
  }

  /**
   * Configure criteria for a team - in v2, this adds a rule and configures it
   */
  async configureTeamCriteria(teamName: string, criteria: {
    geography?: string[];
    roomCount?: string[];
    eventType?: string[];
    industry?: string[];
  }) {
    // Find the team row
    const teamRow = this.page.getByRole('row').filter({ hasText: teamName }).first();

    // Get the team ID from the row's data-testid
    const teamRowTestId = await teamRow.getAttribute('data-testid');
    const teamId = teamRowTestId?.replace('team-row-', '') || '';

    // Click "Add Rule" button for this team using the unique test ID
    await this.page.getByTestId(`add-rule-team-${teamId}`).click();

    // Wait for the Add New Rule dialog - it may not have data-testid, so use text content
    await expect(this.page.getByText('Add New Rule').first()).toBeVisible();
    await expect(this.page.getByText('Add a new assignment rule for')).toBeVisible();

    // Confirm to add the rule
    await this.page.getByRole('button', { name: 'Add Rule' }).click();
    // Wait for dialog to close by checking that the "Add New Rule" text is no longer visible
    await expect(this.page.getByText('Add New Rule').first()).not.toBeVisible();

    // Wait a moment for the rule to be created and UI to update
    await this.page.waitForTimeout(1000);

    // Now the rule is added with empty criteria. For the first rule, criteria are shown in the main team row
    // For subsequent rules, they appear as separate rule rows

    // Check if this is the first rule (displayed in main row) or additional rule (separate row)
    const teamRowElement = this.page.getByTestId(`team-row-${teamId}`);

    // Configure geography if provided
    if (criteria.geography) {
      // Try to find "Not Set" button in the main team row first
      const geographyNotSetButton = teamRowElement.locator('[data-column="geography"] button:has-text("Not Set")');
      const isMainRowButton = await geographyNotSetButton.isVisible().catch(() => false);

      if (isMainRowButton) {
        await geographyNotSetButton.click();
      } else {
        // Look for it in rule rows
        const ruleRows = this.page.locator('[data-row-type="rule"]');
        const newRuleRow = ruleRows.last();
        await newRuleRow.locator('[data-column="geography"] button:has-text("Not Set")').click();
      }

      // Wait for the geography dialog to be visible
      await expect(this.page.getByText('Edit Geography Rule Criteria')).toBeVisible();
      for (const geo of criteria.geography) {
        if (geo.includes(':')) {
          await this.page.getByTestId(geo).click();
        } else {
          await this.page.getByRole('checkbox', { name: geo }).click();
        }
      }
      await this.page.getByRole('button', { name: 'Save' }).click();
      await expect(this.page.getByText('Edit Geography Rule Criteria')).not.toBeVisible();
    }

    if (criteria.roomCount) {
      // Try to find "Not Set" button in the main team row first
      const roomCountNotSetButton = teamRowElement.locator('[data-column="roomCount"] button:has-text("Not Set")');
      const isMainRowButton = await roomCountNotSetButton.isVisible().catch(() => false);

      if (isMainRowButton) {
        await roomCountNotSetButton.click();
      } else {
        // Look for it in rule rows
        const ruleRows = this.page.locator('[data-row-type="rule"]');
        const newRuleRow = ruleRows.last();
        await newRuleRow.locator('[data-column="roomCount"] button:has-text("Not Set")').click();
      }

      // Wait for the room count dialog to be visible
      await expect(this.page.getByText('Edit Room count Rule Criteria')).toBeVisible();

      for (const room of criteria.roomCount) {
        await this.page.getByRole('checkbox', { name: room }).click();
      }
      await this.page.getByRole('button', { name: 'Save' }).click();
      await expect(this.page.getByText('Edit Room count Rule Criteria')).not.toBeVisible();
    }

    if (criteria.eventType) {
      // Try to find "Not Set" button in the main team row first
      const eventTypeNotSetButton = teamRowElement.locator('[data-column="eventType"] button:has-text("Not Set")');
      const isMainRowButton = await eventTypeNotSetButton.isVisible().catch(() => false);

      if (isMainRowButton) {
        await eventTypeNotSetButton.click();
      } else {
        // Look for it in rule rows
        const ruleRows = this.page.locator('[data-row-type="rule"]');
        const newRuleRow = ruleRows.last();
        await newRuleRow.locator('[data-column="eventType"] button:has-text("Not Set")').click();
      }

      // Wait for the event type dialog to be visible
      await expect(this.page.getByText('Edit Event type Rule Criteria')).toBeVisible();

      for (const event of criteria.eventType) {
        await this.page.getByRole('checkbox', { name: event }).click();
      }
      await this.page.getByRole('button', { name: 'Save' }).click();
      await expect(this.page.getByText('Edit Event type Rule Criteria')).not.toBeVisible();
    }

    if (criteria.industry) {
      // Try to find "Not Set" button in the main team row first
      const industryNotSetButton = teamRowElement.locator('[data-column="industry"] button:has-text("Not Set")');
      const isMainRowButton = await industryNotSetButton.isVisible().catch(() => false);

      if (isMainRowButton) {
        await industryNotSetButton.click();
      } else {
        // Look for it in rule rows
        const ruleRows = this.page.locator('[data-row-type="rule"]');
        const newRuleRow = ruleRows.last();
        await newRuleRow.locator('[data-column="industry"] button:has-text("Not Set")').click();
      }

      // Wait for the industry dialog to be visible
      await expect(this.page.getByText('Edit Industry Rule Criteria')).toBeVisible();

      for (const ind of criteria.industry) {
        await this.page.getByRole('checkbox', { name: ind }).click();
      }
      await this.page.getByRole('button', { name: 'Save' }).click();
      await expect(this.page.getByText('Edit Industry Rule Criteria')).not.toBeVisible();
    }
  }

  /**
   * Verify team criteria are displayed correctly
   */
  async verifyTeamCriteria(teamName: string, expectedCriteria: string[]) {
    const teamRow = this.page.getByRole('row').filter({ hasText: teamName });
    const teamId = await teamRow.getAttribute('data-testid');
    const extractedTeamId = teamId?.replace('team-row-', '') || '';
    
    for (const criteria of expectedCriteria) {
      // Check if the criteria appears in any of the team's criteria cells
      const teamCriteriaCells = this.page.locator(`[data-testid^="${extractedTeamId}-"][data-testid$="-criteria-cell"]`);
      const matchingCells = teamCriteriaCells.filter({ hasText: criteria });
      await expect(matchingCells.first()).toBeVisible();
    }
  }

  /**
   * Create a new individual
   */
  async createIndividual(individual: {
    firstName: string;
    lastName: string;
    title: string;
    email: string;
    phone: string;
    teamName?: string;
  }) {
    await this.addIndividualButton.click();
    await expect(this.page.getByRole('heading', { name: 'Add New Individual' })).toBeVisible();
    
    await this.page.getByTestId('individual-first-name-input').fill(individual.firstName);
    await this.page.getByTestId('individual-last-name-input').fill(individual.lastName);
    await this.page.getByTestId('individual-title-input').fill(individual.title);
    await this.page.getByTestId('individual-email-input').fill(individual.email);
    await this.page.getByTestId('individual-phone-input').fill(individual.phone);
    
    if (individual.teamName) {
      await this.page.getByRole('combobox', { name: 'Assign to Team' }).click();
      await this.page.getByRole('option', { name: individual.teamName }).click();
    }
    
    await this.page.getByTestId('individual-save-button').click();
    await expect(this.page.getByRole('dialog')).not.toBeVisible();
  }

  /**
   * Remove an individual by clicking the delete button and confirming
   */
  async removeIndividual(individualName: string) {
    const individualRow = this.page.getByRole('row').filter({ hasText: individualName }).first();
    
    // Get the individual's ID from the row's data-testid
    const rowTestId = await individualRow.getAttribute('data-testid');
    const individualId = rowTestId?.replace('individual-row-', '') || '';
    
    // Click the delete button using the unique test ID
    await this.page.getByTestId(`delete-individual-${individualId}`).click();
    
    // Confirm deletion in the confirmation dialog
    const confirmationDialog = this.page.getByTestId('confirmation-dialog');
    await expect(confirmationDialog).toBeVisible();
    await expect(confirmationDialog).toContainText('Delete Person');
    await expect(confirmationDialog).toContainText(individualName);
    
    await this.page.getByTestId('confirmation-confirm-button').click();
    await expect(confirmationDialog).not.toBeVisible();
    
    // Verify the individual is no longer in the table
    await expect(this.page.getByRole('row').filter({ hasText: individualName })).not.toBeVisible();
  }

  /**
   * Remove a team by clicking the delete button and confirming
   */
  async removeTeam(teamName: string) {
    const teamRow = this.page.getByRole('row').filter({ hasText: teamName }).first();
    
    // Click the delete action button directly (no dropdown menu anymore)
    await teamRow.getByTestId('delete-action').click();
    
    // Confirm deletion in the confirmation dialog
    const confirmationDialog = this.page.getByTestId('confirmation-dialog');
    await expect(confirmationDialog).toBeVisible();
    await expect(confirmationDialog).toContainText('Delete Team');
    await expect(confirmationDialog).toContainText(teamName);
    
    await this.page.getByTestId('confirmation-confirm-button').click();
    await expect(confirmationDialog).not.toBeVisible();
    
    // Verify the team is no longer in the table
    await expect(this.page.getByRole('row').filter({ hasText: teamName })).not.toBeVisible();
  }

  /**
   * Clear all criteria for an individual (uncheck all checkboxes)
   */
  async clearIndividualCriteria(individualIdentifier: string) {
    let individualRow: Locator;
    let individualId: string;
    
    // Handle both testid and text-based identification
    if (individualIdentifier.startsWith('individual-row-')) {
      individualRow = this.page.getByTestId(individualIdentifier);
      individualId = individualIdentifier.replace('individual-row-', '');
    } else {
      individualRow = this.page.getByRole('row').filter({ hasText: individualIdentifier }).first();
      // Get the individual ID from the row's data-testid
      const rowTestId = await individualRow.getAttribute('data-testid');
      individualId = rowTestId?.replace('individual-row-', '') || '';
    }

    // Define all possible criteria types
    const allCriteriaTypes = ['geography', 'roomCount', 'eventType', 'industry'];
    
    // Only clear criteria types that have visible edit buttons
    for (const criteriaType of allCriteriaTypes) {
      const editButton = this.page.getByTestId(`${individualId}-${criteriaType}-edit-button`);
      const isVisible = await editButton.isVisible().catch(() => false);
      
      if (isVisible) {
        await editButton.click();
        const dialog = this.page.getByRole('dialog');
        await expect(dialog).toBeVisible();
        
        // Uncheck all checked checkboxes (custom components with role="checkbox" and data-state="checked")
        const checkedBoxes = dialog.locator('[role="checkbox"][data-state="checked"]');
        const checkedCount = await checkedBoxes.count();
        for (let i = 0; i < checkedCount; i++) {
          await checkedBoxes.nth(i).click();
        }
        await this.page.getByRole('button', { name: 'Save' }).click();
        // Wait for dialog to close by checking that the "Add New Rule" text is no longer visible
    await expect(this.page.getByText('Add New Rule').first()).not.toBeVisible();
      }
    }
  }

  /**
   * Configure individual criteria (replaces existing criteria)
   */
  async configureIndividualCriteria(individualIdentifier: string, criteria: {
    geography?: string[];
    roomCount?: string[];
    eventType?: string[];
    industry?: string[];
  }) {
    let individualRow: Locator;
    let individualId: string;
    
    // Handle both testid and text-based identification
    if (individualIdentifier.startsWith('individual-row-')) {
      individualRow = this.page.getByTestId(individualIdentifier);
      individualId = individualIdentifier.replace('individual-row-', '');
    } else {
      individualRow = this.page.getByRole('row').filter({ hasText: individualIdentifier }).first();
      // Get the individual ID from the row's data-testid
      const rowTestId = await individualRow.getAttribute('data-testid');
      individualId = rowTestId?.replace('individual-row-', '') || '';
    }

    // In v2, we need to add a rule first
    // Click "Add Rule" button for this individual using the unique test ID
    await this.page.getByTestId(`add-rule-individual-${individualId}`).click();
    
    // Wait for the Add New Rule dialog - it may not have data-testid, so use text content
    await expect(this.page.getByText('Add New Rule').first()).toBeVisible();
    await expect(this.page.getByText('Add a new assignment rule for')).toBeVisible();
    
    // Confirm to add the rule
    await this.page.getByRole('button', { name: 'Add Rule' }).click();
    // Wait for dialog to close by checking that the "Add New Rule" text is no longer visible
    await expect(this.page.getByText('Add New Rule').first()).not.toBeVisible();
    
    // Now the rule is added with empty criteria. We need to configure each criteria
    // by clicking on the "Not Set" cells in the rule row
    
    // Find the newly added rule row (should be the last one)
    const ruleRows = this.page.locator('[data-row-type="rule"]');
    const newRuleRow = ruleRows.last();
    
    if (criteria.geography) {
      // Click on the "Not Set" button in the geography column
      await newRuleRow.locator('[data-column="geography"] button:has-text("Not Set")').click();
      // Wait for the geography dialog to be visible
      await expect(this.page.getByText('Edit Geography Rule Criteria')).toBeVisible();
      for (const geo of criteria.geography) {
        if (geo.includes(':')) {
          await this.page.getByTestId(geo).click();
        } else {
          await this.page.getByRole('checkbox', { name: geo }).click();
        }
      }
      await this.page.getByRole('button', { name: 'Save' }).click();
      await expect(this.page.getByText('Edit Geography Rule Criteria')).not.toBeVisible();
    }

    if (criteria.roomCount) {
      // Click on the "Not Set" button in the room count column
      await newRuleRow.locator('[data-column="roomCount"] button:has-text("Not Set")').click();
      // Wait for the room count dialog to be visible
      await expect(this.page.getByText('Edit Room count Rule Criteria')).toBeVisible();
      
      for (const room of criteria.roomCount) {
        await this.page.getByRole('checkbox', { name: room }).click();
      }
      await this.page.getByRole('button', { name: 'Save' }).click();
      await expect(this.page.getByText('Edit Room count Rule Criteria')).not.toBeVisible();
    }

    if (criteria.eventType) {
      // Click on the "Not Set" button in the event type column
      await newRuleRow.locator('[data-column="eventType"] button:has-text("Not Set")').click();
      // Wait for the event type dialog to be visible
      await expect(this.page.getByText('Edit Event type Rule Criteria')).toBeVisible();
      
      for (const event of criteria.eventType) {
        await this.page.getByRole('checkbox', { name: event }).click();
      }
      await this.page.getByRole('button', { name: 'Save' }).click();
      await expect(this.page.getByText('Edit Event type Rule Criteria')).not.toBeVisible();
    }

    if (criteria.industry) {
      // Click on the "Not Set" button in the industry column
      await newRuleRow.locator('[data-column="industry"] button:has-text("Not Set")').click();
      // Wait for the industry dialog to be visible
      await expect(this.page.getByText('Edit Industry Rule Criteria')).toBeVisible();
      
      for (const ind of criteria.industry) {
        await this.page.getByRole('checkbox', { name: ind }).click();
      }
      await this.page.getByRole('button', { name: 'Save' }).click();
      await expect(this.page.getByText('Edit Industry Rule Criteria')).not.toBeVisible();
    }
  }

  /**
   * Verify individual criteria are displayed correctly
   */
  async verifyIndividualCriteria(individualIdentifier: string, expectedCriteria: string[]) {
    let individualId: string;
    
    if (individualIdentifier.startsWith('individual-row-')) {
      individualId = individualIdentifier.replace('individual-row-', '');
    } else {
      const individualRow = this.page.getByRole('row').filter({ hasText: individualIdentifier }).first();
      const rowTestId = await individualRow.getAttribute('data-testid');
      individualId = rowTestId?.replace('individual-row-', '') || '';
    }

    for (const criteria of expectedCriteria) {
      // Check if the criteria appears in any of the individual's criteria cells
      const individualCriteriaCells = this.page.locator(`[data-testid^="${individualId}-"][data-testid$="-criteria-cell"]`);
      const matchingCells = individualCriteriaCells.filter({ hasText: criteria });
      await expect(matchingCells.first()).toBeVisible();
    }
  }

  /**
   * Verify team member inheritance
   */
  async verifyTeamMemberInheritance(memberName: string, teamName: string) {
    const memberRow = this.page.getByRole('row').filter({ hasText: memberName });
    await expect(memberRow).toContainText(`Inherited from ${teamName}`);
  }

  /**
   * Validate rules and handle the validation panel
   */
  async validateRules() {
    await this.validateRulesButton.click();
    
    const validationPanel = this.page.getByTestId('slide-in-panel').filter({ hasText: 'Configuration Validation' });
    await expect(validationPanel).toBeVisible();
    
    // Close validation panel
    await validationPanel.getByRole('button', { name: 'Back' }).click();
    await expect(validationPanel).not.toBeVisible();
  }

  /**
   * Validate rules expecting overlap detection and verify overlapping entities are shown
   */
  async validateRulesWithOverlapDetection(expectedOverlappingEntities: string[]) {
    await this.validateRulesButton.click();
    
    const validationPanel = this.page.getByTestId('slide-in-panel').filter({ hasText: 'Configuration Validation' });
    await expect(validationPanel).toBeVisible();
    
    // Verify that overlap is detected and mentioned in the validation panel
    await expect(validationPanel).toContainText(/overlap/i);
    
    // Verify that the overlapping entities are mentioned in the validation results
    for (const entity of expectedOverlappingEntities) {
      await expect(validationPanel).toContainText(entity);
    }
    
    // Close validation panel
    await validationPanel.getByRole('button', { name: 'Back' }).click();
    await expect(validationPanel).not.toBeVisible();
  }

  /**
   * Navigate to overlap details page and return structured overlap information
   * @param entityName - The name of the entity to view overlap details for
   * @returns Promise<OverlapDetailsData> - Structured overlap information for assertions
   */
  async getOverlapDetails(entityName: string) {
    // Navigate to overlap details page
    const entityRow = this.page.getByRole('row').filter({ hasText: entityName }).first();
    const overlapIndicator = entityRow.getByTestId('overlap-indicator');
    await overlapIndicator.click();
    
    // Get the overlap details panel
    const overlapDetailsPanel = this.page.getByTestId('overlap-details-panel');
    await expect(overlapDetailsPanel).toBeVisible();
    
    // Extract basic page information
    const pageTitle = await overlapDetailsPanel.locator('h2').first().textContent() || '';
    const summaryText = await overlapDetailsPanel.locator('text=/This individual has overlapping rules with \\d+ other individual/i').textContent() || '';
    
    // Extract all overlapping entities
    const allAccordions = overlapDetailsPanel.locator('[data-testid^="overlap-entity-"]');
    const accordionCount = await allAccordions.count();
    
    const overlappingEntities: Array<{
      name: string;
      overlapCount: number;
      hasActualOverlaps: boolean;
      accordion: any;
    }> = [];
    
    // Extract entity information from accordions
    for (let i = 0; i < accordionCount; i++) {
      const accordion = overlapDetailsPanel.getByTestId(`overlap-entity-${i}`);
      const accordionText = await accordion.textContent();
      
      if (accordionText) {
        const nameMatch = accordionText.match(/^(.+?)(\d+ overlapping criteria)$/);
        if (nameMatch) {
          const entityName = nameMatch[1].trim();
          const overlapCountText = nameMatch[2];
          const overlapCount = parseInt(overlapCountText.match(/(\d+)/)?.[1] || '0');
          const hasActualOverlaps = overlapCount > 0;
          
          overlappingEntities.push({
            name: entityName,
            overlapCount,
            hasActualOverlaps,
            accordion
          });
        }
      }
    }
    
    return {
      pageTitle,
      summaryText,
      overlappingEntities,
      panel: overlapDetailsPanel
    };
  }

  /**
   * Expand an entity accordion and return detailed criteria overlap information
   * @param entityAccordion - The accordion element to expand
   * @param overlapDetailsPanel - The overlap details panel
   * @returns Promise<CriteriaOverlapDetails> - Detailed criteria overlap information
   */
  async getEntityCriteriaDetails(entityAccordion: Locator, overlapDetailsPanel: Locator) {
    // Expand the accordion
    await entityAccordion.click();
    
    // Extract criteria details for each type
    const criteriaTypes = ['eventType', 'industry', 'geography', 'roomCount'];
    const criteriaDetails: Array<{
      type: string;
      displayName: string;
      isVisible: boolean;
      hasOverlapsBadge: boolean;
      content: string;
    }> = [];

    const accordionIndex  = await entityAccordion.getAttribute("data-index")
    const accordionContent = entityAccordion.page().getByTestId(`entity-${accordionIndex}-overlap-detail-display-contents`)
    await expect(accordionContent).toBeVisible()
    for (const criteriaType of criteriaTypes) {
      const criteriaDetail = accordionContent.getByTestId(`overlap-detail-${criteriaType}`);
      const isVisible = await criteriaDetail.isVisible().catch(() => false);
      
      let hasOverlapsBadge = false;
      let content = '';
      let displayName = '';
      
      if (isVisible) {
        content = await criteriaDetail.textContent() || '';
        displayName = criteriaType.charAt(0).toUpperCase() + criteriaType.slice(1).replace(/([A-Z])/g, ' $1');
        
        const overlapsBadge = criteriaDetail.locator('text=Overlaps');
        hasOverlapsBadge = await overlapsBadge.isVisible().catch(() => false);
      }
      
      criteriaDetails.push({
        type: criteriaType,
        displayName,
        isVisible,
        hasOverlapsBadge,
        content
      });
    }
    
    return {
      criteriaDetails
    };
  }

  /**
   * Navigate back from overlap details to the main assignment table
   */
  async closeOverlapDetails() {
    const overlapDetailsPanel = this.page.getByTestId('overlap-details-panel');
    const backButton = overlapDetailsPanel.getByRole('button', { name: /back/i }).first();
    await backButton.click();
    await expect(overlapDetailsPanel).not.toBeVisible();
  }

  /**
   * Verify standard overlap guidance content is present on the overlap details page
   * @param overlapDetailsPanel - The overlap details panel locator
   */
  async verifyOverlapGuidanceContent(overlapDetailsPanel: any) {
    // Validate general overlap explanation and guidance
    await expect(overlapDetailsPanel).toContainText(/Overlapping rules mean that when a lead matches both rules/i);
    await expect(overlapDetailsPanel).toContainText(/This can lead to confusion or inconsistent lead assignment/i);
    await expect(overlapDetailsPanel).toContainText('Overlaps that need to be resolved');
  }

  /**
   * Test editing a team by clicking on the team name
   */
  async testTeamEdit(teamName: string, newTeamName: string) {
    const teamRow = this.page.getByRole('row').filter({ hasText: teamName }).first();
    
    // Click on the team name to open edit dialog
    await teamRow.getByText(teamName).click();
    
    // Verify edit dialog opens
    await expect(this.page.getByRole('heading', { name: 'Edit Team' })).toBeVisible();
    
    // Verify current team name is populated
    const teamNameInput = this.page.getByTestId('team-name-input');
    await expect(teamNameInput).toHaveValue(teamName);
    
    // Change the team name
    await teamNameInput.clear();
    await teamNameInput.fill(newTeamName);
    
    // Save changes
    await this.page.getByTestId('team-save-button').click();
    await expect(this.page.getByRole('dialog')).not.toBeVisible();
    
    // Verify the team name was updated in the table
    const updatedTeamRow = this.page.getByRole('row').filter({ hasText: newTeamName }).first();
    await expect(updatedTeamRow).toBeVisible();
  }

  /**
   * Test editing an individual by clicking on their name
   */
  async testIndividualEdit(individualName: string, newDetails: {
    firstName?: string;
    lastName?: string;
    title?: string;
    email?: string;
    phone?: string;
  }) {
    const individualRow = this.page.getByRole('row').filter({ hasText: individualName }).first();
    
    // Click on the individual name to open edit dialog
    await individualRow.getByText(individualName).click();
    
    // Verify edit dialog opens
    await expect(this.page.getByRole('heading', { name: 'Edit Individual' })).toBeVisible();
    
    // Verify current details are populated and update if new values provided
    if (newDetails.firstName) {
      const firstNameInput = this.page.getByTestId('individual-first-name-input');
      await expect(firstNameInput).not.toBeEmpty();
      await firstNameInput.clear();
      await firstNameInput.fill(newDetails.firstName);
    }
    
    if (newDetails.lastName) {
      const lastNameInput = this.page.getByTestId('individual-last-name-input');
      await expect(lastNameInput).not.toBeEmpty();
      await lastNameInput.clear();
      await lastNameInput.fill(newDetails.lastName);
    }
    
    if (newDetails.title) {
      const titleInput = this.page.getByTestId('individual-title-input');
      await expect(titleInput).not.toBeEmpty();
      await titleInput.clear();
      await titleInput.fill(newDetails.title);
    }
    
    if (newDetails.email) {
      const emailInput = this.page.getByTestId('individual-email-input');
      await expect(emailInput).not.toBeEmpty();
      await emailInput.clear();
      await emailInput.fill(newDetails.email);
    }
    
    if (newDetails.phone) {
      const phoneInput = this.page.getByTestId('individual-phone-input');
      await expect(phoneInput).not.toBeEmpty();
      await phoneInput.clear();
      await phoneInput.fill(newDetails.phone);
    }
    
    // Save changes
    await this.page.getByTestId('individual-save-button').click();
    await expect(this.page.getByRole('dialog')).not.toBeVisible();
    
    // Verify the individual details were updated in the table
    if (newDetails.firstName && newDetails.lastName) {
      const newFullName = `${newDetails.firstName} ${newDetails.lastName}`;
      await expect(this.page.getByText(newFullName)).toBeVisible();
    }
  }

  /**
   * Verify that teams and individuals are displayed correctly in the assignment table
   */
  async verifyEntitiesInTable(teams: string[], individuals: string[]) {
    // Verify teams are displayed
    for (const team of teams) {
      const teamRow = this.page.getByRole('row').filter({ hasText: team }).first();
      await expect(teamRow).toBeVisible();
      await expect(teamRow.getByText(team)).toBeVisible();
    }
    
    // Verify individuals are displayed
    for (const individual of individuals) {
      const individualRow = this.page.getByRole('row').filter({ hasText: individual }).first();
      await expect(individualRow).toBeVisible();
      await expect(individualRow.getByText(individual)).toBeVisible();
    }
  }

  /**
   * Test that clicking on team/individual names opens edit dialogs without making changes
   */
  async testEditDialogAccess(entityName: string, entityType: 'team' | 'individual') {
    const entityRow = this.page.getByRole('row').filter({ hasText: entityName }).first();
    
    // Click on the entity name
    await entityRow.getByText(entityName).click();
    
    // Verify appropriate edit dialog opens
    const expectedHeading = entityType === 'team' ? 'Edit Team' : 'Edit Individual';
    await expect(this.page.getByRole('heading', { name: expectedHeading })).toBeVisible();
    
    // Verify form fields are populated (not empty)
    if (entityType === 'team') {
      await expect(this.page.getByTestId('team-name-input')).not.toBeEmpty();
    } else {
      await expect(this.page.getByTestId('individual-first-name-input')).not.toBeEmpty();
      await expect(this.page.getByTestId('individual-last-name-input')).not.toBeEmpty();
      await expect(this.page.getByTestId('individual-email-input')).not.toBeEmpty();
    }
    
    // Close dialog without saving
    await this.page.getByTestId(`${entityType}-cancel-button`).click();
    await expect(this.page.getByRole('dialog')).not.toBeVisible();
  }

  /**
   * Verify that specific criteria are unset (showing "Not Set" or "Any") using entity-specific data-testids
   */
  async verifyUnsetCriteria(entityIdentifier: string, entityType: 'team' | 'individual', unsetCriteria: string[]) {
    let entityId: string;
    
    // Extract entity ID from identifier
    if (entityIdentifier.startsWith(`${entityType}-row-`)) {
      entityId = entityIdentifier.replace(`${entityType}-row-`, '');
    } else {
      // Get ID from the row's data-testid
      const entityRow = this.page.getByRole('row').filter({ hasText: entityIdentifier }).first();
      const rowTestId = await entityRow.getAttribute('data-testid');
      entityId = rowTestId?.replace(`${entityType}-row-`, '') || '';
    }

    // In v2, entities have multiple rules instead of single criteria
    // We need to check if the entity has any rules defined
    const entityRow = entityType === 'team' 
      ? this.page.getByTestId(`team-row-${entityId}`)
      : this.page.getByRole('row').filter({ has: this.page.getByTestId(`individual-${entityId}`) });
    
    // For v2, we verify that the entity exists but may not have rules yet
    await expect(entityRow).toBeVisible();
  }

  /**
   * Verify that specific criteria are set (not showing "Not Set" or "Any") using entity-specific data-testids
   */
  async verifySetCriteria(entityIdentifier: string, entityType: 'team' | 'individual', setCriteria: string[]) {
    let entityId: string;
    
    // Extract entity ID from identifier
    if (entityIdentifier.startsWith(`${entityType}-row-`)) {
      entityId = entityIdentifier.replace(`${entityType}-row-`, '');
    } else {
      // Get ID from the row's data-testid
      const entityRow = this.page.getByRole('row').filter({ hasText: entityIdentifier }).first();
      const rowTestId = await entityRow.getAttribute('data-testid');
      entityId = rowTestId?.replace(`${entityType}-row-`, '') || '';
    }

    for (const criteriaType of setCriteria) {
      const criteriaCell = this.page.getByTestId(`${entityId}-${criteriaType}`);
      
      // Should not contain "Not Set" or "Any"
      await expect(criteriaCell).not.toContainText('Not Set');
      await expect(criteriaCell).not.toContainText('Any');
    }
  }


  async verifyValidationPanelShowsNoCoverageGaps(){
   await  expect(this.page.getByTestId("no-validation-issues-found")).toBeVisible()
  }

  /**
   * Get coverage gap details from the validation panel
   */
  async getCoverageGapDetails() {
    const validationPanel = this.page.getByTestId('slide-in-panel').filter({hasText: 'Configuration Validation'});
    await expect(validationPanel).toBeVisible();
    
    const summary = await validationPanel.getByTestId('coverage-gap-summary');
    const countText = await summary.getByTestId('coverage-gap-count').textContent();
    const percentageText = await summary.getByTestId('coverage-gap-percentage').textContent();
    
    // Extract numbers from text
    const gapCount = parseInt(countText?.match(/(\d+) unassigned lead scenarios/)?.[1] || '0');
    const percentage = parseInt(percentageText?.match(/(\d+)%/)?.[1] || '0');
    
    return {
      gapCount,
      percentage,
      countText,
      percentageText
    };
  }

  /**
   * Helper to convert criteria values to normalized IDs for data-testid
   */
  private normalizeCriteriaValue(value: string): string {
    return value.toLowerCase().replace(/\s+/g, '-');
  }

  /**
   * Build coverage gap row testid from criteria
   */
  private buildCoverageGapRowTestId(criteria: Record<string, string>): string {
    // Sort criteria by type to ensure consistent ordering
    const sortedEntries = Object.entries(criteria)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([type, value]) => `${type}-${this.normalizeCriteriaValue(value)}`);
    
    return `coverage-gap-row-${sortedEntries.join('_')}`;
  }

  /**
   * Verify table coverage gaps exist
   */
  async verifyTableCoverageGaps(expectedGaps: Array<Record<string, string>>) {
    const validationPanel = this.page.getByTestId('slide-in-panel').filter({hasText: 'Configuration Validation'});
    const table = validationPanel.getByTestId('coverage-gap-table');
    await expect(table).toBeVisible();
    
    for (const gap of expectedGaps) {
      const testId = this.buildCoverageGapRowTestId(gap);
      const gapRow = table.getByTestId(testId);
      await expect(gapRow).toBeVisible();
      
      // Verify gap icons are present in the row
      const warningIcons = gapRow.locator('.lucide-triangle-alert');
      await expect(warningIcons.first()).toBeVisible();
    }
  }

  /**
   * Verify specific coverage gaps do NOT exist (are covered)
   */
  async verifyTableCoveredScenarios(coveredScenarios: Array<Record<string, string>>) {
    const validationPanel = this.page.getByTestId('slide-in-panel').filter({hasText: 'Configuration Validation'});
    const table = validationPanel.getByTestId('coverage-gap-table');
    await expect(table).toBeVisible();
    
    for (const scenario of coveredScenarios) {
      const testId = this.buildCoverageGapRowTestId(scenario);
      const gapRow = table.getByTestId(testId);
      // These scenarios should NOT appear in the gap table
      await expect(gapRow).not.toBeVisible();
    }
  }

  /**
   * Get table column headers
   */
  async getTableColumnHeaders(): Promise<string[]> {
    const validationPanel = this.page.getByTestId('slide-in-panel').filter({hasText: 'Configuration Validation'});
    const table = validationPanel.getByTestId('coverage-gap-table');
    
    const headers: string[] = [];
    const headerCells = table.getByTestId(/coverage-gap-table-header-/);
    const count = await headerCells.count();
    
    for (let i = 0; i < count; i++) {
      const text = await headerCells.nth(i).textContent();
      if (text) headers.push(text.trim());
    }
    
    return headers;
  }

  /**
   * Verify table has expected column headers
   */
  async verifyTableHeaders(expectedHeaders: string[]) {
    const actualHeaders = await this.getTableColumnHeaders();
    expect(actualHeaders).toEqual(expectedHeaders);
  }


  /**
   * Acknowledge coverage gaps in the validation panel
   */
  async acknowledgeCoverageGaps() {
    const validationPanel = this.page.getByTestId('slide-in-panel').filter({hasText: 'Configuration Validation'});
    
    // Verify checkbox starts unchecked
    const acknowledgeCheckbox = validationPanel.getByTestId('acknowledge-gaps-checkbox');
    await expect(acknowledgeCheckbox).toHaveAttribute('data-state', 'unchecked');
    
    // Click to check the checkbox
    await acknowledgeCheckbox.click();
    await expect(acknowledgeCheckbox).toHaveAttribute('data-state', 'checked');
    
    // Click the acknowledge button
    const acknowledgeButton = validationPanel.getByTestId('acknowledge-gaps-button');
    await expect(acknowledgeButton).toBeEnabled();
    await expect(acknowledgeButton).toContainText('Accept Gaps & Continue');
    await acknowledgeButton.click();
    
    // Verify validation panel closes
    await expect(validationPanel).not.toBeVisible();
  }

  /**
   * Set specific criteria to "Any" (no checkboxes selected)
   */
  async setIndividualCriteriaToAny(individualIdentifier: string, criteriaTypes: string[]) {
    let individualId: string;
    
    // Handle both testid and text-based identification
    if (individualIdentifier.startsWith('individual-row-')) {
      individualId = individualIdentifier.replace('individual-row-', '');
    } else {
      const individualRow = this.page.getByRole('row').filter({ hasText: individualIdentifier }).first();
      const rowTestId = await individualRow.getAttribute('data-testid');
      individualId = rowTestId?.replace('individual-row-', '') || '';
    }

    for (const criteriaType of criteriaTypes) {
      // Check if the edit button exists and is visible before trying to interact with it
      const editButton = this.page.getByTestId(`${individualId}-${criteriaType}-edit-button`);
      const isVisible = await editButton.isVisible().catch(() => false);
      
      if (isVisible) {
        console.log(`Setting ${criteriaType} to "Any" for individual ${individualIdentifier}`);
        
        // Open the criteria dialog
        await editButton.click();
        const dialog = this.page.getByRole('dialog');
        await expect(dialog).toBeVisible();
        
        // Look for and click the explicit "Any" option
        const anyOption = dialog.getByRole('checkbox', { name: 'Any' });
        const anyOptionExists = await anyOption.isVisible().catch(() => false);
        
        if (anyOptionExists) {
          console.log(`Found "Any" checkbox for ${criteriaType}, clicking it`);
          await anyOption.click();
        } else {
          // Fallback: look for "Any" as text or other selectors
          const anyText = dialog.getByText('Any');
          const anyTextExists = await anyText.isVisible().catch(() => false);
          
          if (anyTextExists) {
            console.log(`Found "Any" text for ${criteriaType}, clicking it`);
            await anyText.click();
          } else {
            console.log(`No "Any" option found for ${criteriaType}, unchecking all boxes as fallback`);
            // Fallback to original behavior: uncheck all boxes
            const checkedBoxes = dialog.locator('[role="checkbox"][data-state="checked"]');
            const checkedCount = await checkedBoxes.count();
            console.log(`Found ${checkedCount} checked boxes for ${criteriaType}`);
            
            for (let i = 0; i < checkedCount; i++) {
              await checkedBoxes.nth(i).click();
            }
          }
        }
        
        // Save the dialog
        await this.page.getByRole('button', { name: 'Save' }).click();
        // Wait for dialog to close by checking that the "Add New Rule" text is no longer visible
    await expect(this.page.getByText('Add New Rule').first()).not.toBeVisible();
        
        // Wait a moment for the change to be processed
        await this.page.waitForTimeout(500);
        
        // Verify the criteria cell shows "Any" or similar
        const criteriaCell = this.page.getByTestId(`${individualId}-${criteriaType}-criteria-cell`);
        const cellText = await criteriaCell.textContent();
        console.log(`After setting to Any, ${criteriaType} cell shows: "${cellText}"`);
      }
    }
  }

  /**
   * Get overlap detail display data for a specific entity accordion
   * @param entityAccordion - The entity accordion locator
   * @param overlapDetailsPanel - The overlap details panel locator
   * @returns Structured overlap detail display data
   */
  async getOverlapDetailDisplayData(entityAccordion: Locator, overlapDetailsPanel: Locator) {
    // Expand the accordion
    await entityAccordion.click();
    
    // Get the accordion index and scope to the specific entity content
    const accordionIndex = await entityAccordion.getAttribute("data-index");
    const accordionContent = overlapDetailsPanel.getByTestId(`entity-${accordionIndex}-overlap-detail-display-contents`);
    await expect(accordionContent).toBeVisible();
    
    // Get the single overlap detail display within this accordion
    const singleOverlapDisplay = accordionContent.getByTestId('single-overlap-detail-display');
    await expect(singleOverlapDisplay).toBeVisible();
    
    // Extract criteria data
    const criteriaTypes = ['geography', 'roomCount', 'eventType', 'industry'];
    const criteriaData: Array<{
      type: string;
      isVisible: boolean;
      hasOverlapsBadge: boolean;
      hasDestructiveStyling: boolean;
      entity1Values: string[];
      entity2Values: string[];
      overlapExplanation: string;
    }> = [];
    
    for (const criteriaType of criteriaTypes) {
      const criteriaCard = singleOverlapDisplay.getByTestId(`overlap-detail-${criteriaType}`);
      const isVisible = await criteriaCard.isVisible().catch(() => false);
      
      if (isVisible) {
        // Check for overlap badge
        const overlapsBadge = criteriaCard.getByText('Overlaps');
        const hasOverlapsBadge = await overlapsBadge.isVisible().catch(() => false);
        
        // Check for destructive styling
        const hasDestructiveStyling = await criteriaCard.evaluate((el, type) => {
          const classList = Array.from(el.classList);
          const hasDestructiveBorder = classList.some(cls => cls.includes('border-destructive'));
          const hasDestructiveBackground = classList.some(cls => cls.includes('bg-destructive'));
          console.log(`Criteria ${type} classes:`, classList);
          console.log(`Has destructive border: ${hasDestructiveBorder}, Has destructive background: ${hasDestructiveBackground}`);
          return hasDestructiveBorder || hasDestructiveBackground; // More lenient check
        }, criteriaType);
        
        // Get entity values
        const entity1Values: string[] = [];
        const entity2Values: string[] = [];
        
        const entity1Locators = criteriaCard.locator(`[data-testid^="entity1-${criteriaType}-value-"]`);
        const entity1Count = await entity1Locators.count();
        for (let i = 0; i < entity1Count; i++) {
          const value = await entity1Locators.nth(i).textContent();
          if (value) entity1Values.push(value.trim());
        }
        
        const entity2Locators = criteriaCard.locator(`[data-testid^="entity2-${criteriaType}-value-"]`);
        const entity2Count = await entity2Locators.count();
        for (let i = 0; i < entity2Count; i++) {
          const value = await entity2Locators.nth(i).textContent();
          if (value) entity2Values.push(value.trim());
        }
        
        // Get overlap explanation
        const explanationBox = criteriaCard.getByTestId(`overlap-explanation-box-${criteriaType}`);
        const explanationText = explanationBox.getByTestId('overlap-explanation');
        const overlapExplanation = await explanationText.textContent() || '';
        
        criteriaData.push({
          type: criteriaType,
          isVisible: true,
          hasOverlapsBadge,
          hasDestructiveStyling,
          entity1Values,
          entity2Values,
          overlapExplanation
        });
      } else {
        criteriaData.push({
          type: criteriaType,
          isVisible: false,
          hasOverlapsBadge: false,
          hasDestructiveStyling: false,
          entity1Values: [],
          entity2Values: [],
          overlapExplanation: ''
        });
      }
    }
    
    return {
      criteriaData,
      isVisible: true
    };
  }

  /**
   * Verify overlap detail display data matches expected criteria
   * @param entityAccordion - The entity accordion locator
   * @param overlapDetailsPanel - The overlap details panel locator
   * @param expectedOverlaps - Expected overlap data for verification
   */
  async verifyOverlapDetailDisplay(
    entityAccordion: Locator,
    overlapDetailsPanel: Locator,
    expectedOverlaps: Record<string, { entity1Values: string[], entity2Values: string[] }>
  ) {
    // Get the structured data
    const overlapData = await this.getOverlapDetailDisplayData(entityAccordion, overlapDetailsPanel);
    
    // Verify all expected criteria are present and have correct data
    const expectedCriteriaTypes = Object.keys(expectedOverlaps);
    
    for (const criteriaType of expectedCriteriaTypes) {
      const criteriaInfo = overlapData.criteriaData.find(c => c.type === criteriaType);
      expect(criteriaInfo).toBeDefined();
      expect(criteriaInfo!.isVisible).toBe(true);
      expect(criteriaInfo!.hasOverlapsBadge).toBe(true);
      // Note: Destructive styling check is more lenient - depends on implementation
      // expect(criteriaInfo!.hasDestructiveStyling).toBe(true);
      
      const expectedData = expectedOverlaps[criteriaType];
      expect(criteriaInfo!.entity1Values).toEqual(expectedData.entity1Values);
      expect(criteriaInfo!.entity2Values).toEqual(expectedData.entity2Values);
      
      // Verify overlap explanation contains relevant text
      expect(criteriaInfo!.overlapExplanation).toMatch(/overlap|share|both/i);
    }
    
    // Collapse the accordion
    await entityAccordion.click();
    
    return overlapData;
  }

  /**
   * Get validation panel data structure
   * @returns Structured validation panel information
   */
  async getValidationPanelData() {
    await this.validateRulesButton.click();
    
    const validationPanel = this.page.getByTestId('slide-in-panel').filter({hasText: 'Configuration Validation'});
    const isVisible = await validationPanel.isVisible();
    
    if (!isVisible) {
      return {
        isVisible: false,
        hasOverlapText: false,
        hasWarningText: false,
        content: ''
      };
    }
    
    const content = await validationPanel.textContent() || '';
    const hasOverlapText = /overlap/i.test(content);
    const hasWarningText = /These gaps must be acknowledged/i.test(content);
    
    return {
      isVisible: true,
      hasOverlapText,
      hasWarningText,
      content
    };
  }

  /**
   * Close validation panel
   */
  async closeValidationPanel() {
    const validationPanel = this.page.getByTestId('slide-in-panel').filter({hasText: 'Configuration Validation'});
    const validationBackButton = validationPanel.getByRole('button', {name: /back/i}).first();
    await validationBackButton.click();
    await expect(validationPanel).not.toBeVisible();
  }

  /**
   * Add an exception for an individual
   * @param individualName - Name of the individual
   * @param exception - Exception configuration
   */
  async addException(individualName: string, exception: {
    criteria: string;
    operator: string;
    value: string;
    action: string;
  }) {
    const individualRow = this.page.getByRole('row').filter({hasText: individualName}).first();
    await individualRow.getByTestId('exception-action').click();

    const exceptionModal = this.page.getByTestId('slide-in-panel').filter({hasText: `Manage Exceptions for Individual: ${individualName}`});
    await expect(exceptionModal).toBeVisible();

    // Add new exception
    await exceptionModal.getByRole('button', {name: 'Add New Exception'}).click();

    // Configure the exception condition
    await exceptionModal.getByRole('combobox').first().click(); // Criteria dropdown
    await this.page.getByRole('option', {name: exception.criteria}).click();

    await exceptionModal.getByRole('combobox').nth(1).click(); // Operator dropdown
    await this.page.getByRole('option').getByText(exception.operator, {exact: true}).click();

    await exceptionModal.getByRole('combobox').nth(2).click(); // Value dropdown
    await this.page.getByRole('option', {name: exception.value}).click();

    // Set action
    const actionRegex = new RegExp(exception.action, 'i');
    await exceptionModal.getByRole('radio', {name: actionRegex}).click();

    // Save the exception
    await exceptionModal.getByRole('button', {name: 'Save Exception'}).click();

    // Save all exceptions and close modal
    await exceptionModal.getByRole('button', {name: 'Save All Exceptions'}).click();
    await expect(exceptionModal).not.toBeVisible();
  }

  /**
   * Remove default individual if present
   * @param individualName - Name of the default individual to remove
   */
  async removeDefaultIndividualIfPresent(individualName: string) {
    const individualRow = this.page.getByRole('row').filter({hasText: individualName});
    if (await individualRow.count() > 0) {
      await this.removeIndividual(individualName);
    }
  }

  /**
   * Save and activate configuration with verification
   * @returns Result of the save and activation process
   */
  async saveAndActivateWithVerification() {
    const wasEnabled = await this.saveActivateButton.isEnabled();
    
    if (!wasEnabled) {
      return {
        success: false,
        message: 'Save & Activate button was disabled',
        buttonEnabled: false
      };
    }
    
    await this.saveActivateButton.click();
    
    // Check for success message
    const successMessage = this.page.getByText(/Rules Successfully Activated!/i);
    const isSuccessVisible = await successMessage.isVisible().catch(() => false);
    
    if (isSuccessVisible) {
      const messageText = await successMessage.textContent() || '';
      return {
        success: true,
        message: messageText,
        buttonEnabled: true
      };
    }
    
    // Check for error message or other outcomes
    const errorMessage = this.page.getByText(/error|failed/i);
    const isErrorVisible = await errorMessage.isVisible().catch(() => false);
    
    if (isErrorVisible) {
      const messageText = await errorMessage.textContent() || '';
      return {
        success: false,
        message: messageText,
        buttonEnabled: true
      };
    }
    
    return {
      success: false,
      message: 'No clear success or error message found',
      buttonEnabled: true
    };
  }

  /**
   * Get validation panel data with coverage gaps
   * @returns Enhanced validation data including coverage information
   */
  async getValidationDataWithCoverageGaps() {
    const basicData = await this.getValidationPanelData();
    
    if (!basicData.isVisible) {
      return {
        ...basicData,
        coverageGaps: null,
        hasTable: false
      };
    }
    
    const validationPanel = this.page.getByTestId('slide-in-panel').filter({hasText: 'Configuration Validation'});
    
    // Check if coverage gap table exists
    const coverageTable = validationPanel.getByTestId('coverage-gap-table');
    const hasTable = await coverageTable.isVisible().catch(() => false);
    
    let coverageGaps = null;
    if (hasTable) {
      coverageGaps = await this.getCoverageGapDetails();
    }
    
    return {
      ...basicData,
      coverageGaps,
      hasTable
    };
  }
} 
