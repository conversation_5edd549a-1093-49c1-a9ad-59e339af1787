import { relations } from "drizzle-orm/relations";
import {
  managementGroup,
  property,
  stsIntegrationSettings,
  highlevelWebhookIntegrationSettings,
  form,
  formSubmission,
  theme,
  formField,
  lead,
  integrationLeadSyncLog,
  leadFile,
  formFieldOption,
  managementGroupApiKey,
} from "./schema";

export const propertyRelations = relations(property, ({ one, many }) => ({
  managementGroup: one(managementGroup, {
    fields: [property.managementGroupId],
    references: [managementGroup.id],
  }),
  stsIntegrationSettings: many(stsIntegrationSettings),
  highlevelWebhookIntegrationSettings: many(
    highlevelWebhookIntegrationSettings,
  ),
  forms: many(form),
  leads: many(lead),
}));

export const managementGroupRelations = relations(
  managementGroup,
  ({ many }) => ({
    properties: many(property),
    managementGroupApiKeys: many(managementGroupApiKey),
  }),
);

export const stsIntegrationSettingsRelations = relations(
  stsIntegrationSettings,
  ({ one }) => ({
    property: one(property, {
      fields: [stsIntegrationSettings.propertyId],
      references: [property.id],
    }),
  }),
);

export const highlevelWebhookIntegrationSettingsRelations = relations(
  highlevelWebhookIntegrationSettings,
  ({ one }) => ({
    property: one(property, {
      fields: [highlevelWebhookIntegrationSettings.propertyId],
      references: [property.id],
    }),
  }),
);

export const formSubmissionRelations = relations(formSubmission, ({ one }) => ({
  form: one(form, {
    fields: [formSubmission.formId],
    references: [form.id],
  }),
}));

export const formRelations = relations(form, ({ one, many }) => ({
  formSubmissions: many(formSubmission),
  property: one(property, {
    fields: [form.propertyId],
    references: [property.id],
  }),
  theme: one(theme, {
    fields: [form.themeId],
    references: [theme.id],
  }),
  formFields: many(formField),
}));

export const themeRelations = relations(theme, ({ many }) => ({
  forms: many(form),
}));

export const formFieldRelations = relations(formField, ({ one, many }) => ({
  form: one(form, {
    fields: [formField.formId],
    references: [form.id],
  }),
  formFieldOptions: many(formFieldOption),
}));

export const leadRelations = relations(lead, ({ one, many }) => ({
  property: one(property, {
    fields: [lead.propertyId],
    references: [property.id],
  }),
  integrationLeadSyncLogs: many(integrationLeadSyncLog),
  leadFiles: many(leadFile),
}));

export const integrationLeadSyncLogRelations = relations(
  integrationLeadSyncLog,
  ({ one }) => ({
    lead: one(lead, {
      fields: [integrationLeadSyncLog.leadId],
      references: [lead.id],
    }),
  }),
);

export const leadFileRelations = relations(leadFile, ({ one }) => ({
  lead: one(lead, {
    fields: [leadFile.leadId],
    references: [lead.id],
  }),
}));

export const formFieldOptionRelations = relations(
  formFieldOption,
  ({ one }) => ({
    formField: one(formField, {
      fields: [formFieldOption.formFieldId],
      references: [formField.id],
    }),
  }),
);

export const managementGroupApiKeyRelations = relations(
  managementGroupApiKey,
  ({ one }) => ({
    managementGroup: one(managementGroup, {
      fields: [managementGroupApiKey.managementGroupId],
      references: [managementGroup.id],
    }),
  }),
);
