import {
  pgTable,
  index,
  integer,
  varchar,
  timestamp,
  boolean,
  uuid,
  foreignKey,
  uniqueIndex,
  check,
  jsonb,
  text,
  unique,
  date,
  numeric,
  pgEnum,
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";

export const eventNeed = pgEnum("event_need", [
  "MEETING_SPACE",
  "CATERING",
  "GUESTROOMS",
  "ACTIVITIES",
  "WEDDING_CEREMONY",
  "WEDDING_RECEPTION",
  "REHEARSAL",
  "REHEARSAL_DINNER",
  "SENDOFF_BRUNCH",
  "HONEYMOON",
  "BACHELOR_PARTY",
]);
export const eventType = pgEnum("event_type", [
  "corporate_meeting",
  "corporate_retreat",
  "association_event",
  "wedding",
  "family_reunion",
  "celebration",
  "social_or_sport_club",
]);
export const fieldType = pgEnum("field_type", [
  "EVENT_TYPE",
  "FIRST_NAME",
  "LAST_NAME",
  "COMPANY",
  "EMAIL",
  "PHONE",
  "CITY",
  "STATE",
  "POSTAL_CODE",
  "START_DATE",
  "END_DATE",
  "GUEST_COUNT",
  "ROOM_COUNT",
  "MEAL_COUNT",
  "BUDGET",
  "EVENT_DESCRIPTION",
  "FLEXIBLE_DATES",
  "EVENT_NEEDS",
  "MARKETING_CONSENT",
  "EVENT_NAME",
  "EVENT_DATE_RANGE",
  "COUNTRY",
  "FILE_UPLOAD",
]);
export const fieldWidth = pgEnum("field_width", ["full", "half"]);
export const integrationType = pgEnum("integration_type", ["sts"]);
export const leadSource = pgEnum("lead_source", ["form_submission", "api"]);

export const flywaySchemaHistory = pgTable(
  "flyway_schema_history",
  {
    installedRank: integer("installed_rank").primaryKey().notNull(),
    version: varchar({ length: 50 }),
    description: varchar({ length: 200 }).notNull(),
    type: varchar({ length: 20 }).notNull(),
    script: varchar({ length: 1000 }).notNull(),
    checksum: integer(),
    installedBy: varchar("installed_by", { length: 100 }).notNull(),
    installedOn: timestamp("installed_on", { mode: "string" })
      .defaultNow()
      .notNull(),
    executionTime: integer("execution_time").notNull(),
    success: boolean().notNull(),
  },
  (table) => [
    index("flyway_schema_history_s_idx").using(
      "btree",
      table.success.asc().nullsLast().op("bool_ops"),
    ),
  ],
);

export const managementGroup = pgTable("management_group", {
  id: uuid().defaultRandom().primaryKey().notNull(),
  name: varchar({ length: 255 }).notNull(),
  createdAt: timestamp("created_at", {
    withTimezone: true,
    mode: "string",
  }).default(sql`CURRENT_TIMESTAMP`),
  updatedAt: timestamp("updated_at", { withTimezone: true, mode: "string" }),
});

export const property = pgTable(
  "property",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    name: varchar({ length: 255 }).notNull(),
    country: varchar({ length: 255 }).notNull(),
    region: varchar({ length: 255 }).notNull(),
    city: varchar({ length: 255 }).notNull(),
    addressLine1: varchar("address_line_1", { length: 255 }).notNull(),
    addressLine2: varchar("address_line_2", { length: 255 }),
    postalCode: varchar("postal_code", { length: 15 }).notNull(),
    leadAssignmentIntegrationToken: varchar(
      "lead_assignment_integration_token",
      { length: 255 },
    ),
    leadAssignmentIntegrationLocationId: varchar(
      "lead_assignment_integration_location_id",
      { length: 255 },
    ),
    leadAssignmentIntegrationPipelineId: varchar(
      "lead_assignment_integration_pipeline_id",
      { length: 255 },
    ),
    managementGroupId: uuid("management_group_id"),
  },
  (table) => [
    index("idx_property_management_group_id").using(
      "btree",
      table.managementGroupId.asc().nullsLast().op("uuid_ops"),
    ),
    foreignKey({
      columns: [table.managementGroupId],
      foreignColumns: [managementGroup.id],
      name: "property_management_group_id_fkey",
    }),
  ],
);

export const stsIntegrationSettings = pgTable(
  "sts_integration_settings",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    propertyId: uuid("property_id").notNull(),
    rfpFormId: varchar("rfp_form_id", { length: 255 }).notNull(),
    stsUsername: varchar("sts_username", { length: 255 }).notNull(),
    apiKey: varchar("api_key", { length: 255 }).notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .default(sql`CURRENT_TIMESTAMP`)
      .notNull(),
    updatedAt: timestamp("updated_at", { withTimezone: true, mode: "string" })
      .default(sql`CURRENT_TIMESTAMP`)
      .notNull(),
  },
  (table) => [
    uniqueIndex("idx_sts_integration_settings_property_id").using(
      "btree",
      table.propertyId.asc().nullsLast().op("uuid_ops"),
    ),
    uniqueIndex("sts_integration_settings_api_key_idx").using(
      "btree",
      table.apiKey.asc().nullsLast().op("text_ops"),
    ),
    foreignKey({
      columns: [table.propertyId],
      foreignColumns: [property.id],
      name: "sts_integration_settings_property_id_fkey",
    }).onDelete("cascade"),
  ],
);

export const highlevelWebhookIntegrationSettings = pgTable(
  "highlevel_webhook_integration_settings",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    propertyId: uuid("property_id").notNull(),
    webhookSecret: varchar("webhook_secret", { length: 255 }).notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp("updated_at", { withTimezone: true, mode: "string" }),
  },
  (table) => [
    uniqueIndex("idx_highlevel_webhook_integration_settings_property_id").using(
      "btree",
      table.propertyId.asc().nullsLast().op("uuid_ops"),
    ),
    foreignKey({
      columns: [table.propertyId],
      foreignColumns: [property.id],
      name: "highlevel_webhook_integration_settings_property_id_fkey",
    }).onDelete("cascade"),
  ],
);

export const formSubmission = pgTable(
  "form_submission",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    formId: uuid("form_id").notNull(),
    data: jsonb().notNull(),
    metadata: jsonb().default({}).notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
  },
  (table) => [
    index("idx_form_submission_form_id").using(
      "btree",
      table.formId.asc().nullsLast().op("uuid_ops"),
    ),
    foreignKey({
      columns: [table.formId],
      foreignColumns: [form.id],
      name: "form_submission_form_id_fkey",
    }).onDelete("cascade"),
    check("event_needs_no_duplicates", sql`CHECK (validate_event_needs(data`),
  ],
);

export const form = pgTable(
  "form",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    name: varchar({ length: 255 }).notNull(),
    description: text(),
    propertyId: uuid("property_id").notNull(),
    themeId: uuid("theme_id").notNull(),
    redirectUrl: text("redirect_url"),
    allowedDomains: text("allowed_domains").array().default([""]).notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp("updated_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
  },
  (table) => [
    index("idx_form_property_id").using(
      "btree",
      table.propertyId.asc().nullsLast().op("uuid_ops"),
    ),
    index("idx_form_theme_id").using(
      "btree",
      table.themeId.asc().nullsLast().op("uuid_ops"),
    ),
    foreignKey({
      columns: [table.propertyId],
      foreignColumns: [property.id],
      name: "form_property_id_fkey",
    }).onDelete("cascade"),
    foreignKey({
      columns: [table.themeId],
      foreignColumns: [theme.id],
      name: "form_theme_id_fkey",
    }),
    check("form_redirect_url_check", sql`redirect_url ~ '^https?://'::text`),
  ],
);

export const theme = pgTable(
  "theme",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    name: varchar({ length: 255 }).notNull(),
    primaryColor: varchar("primary_color", { length: 50 }).notNull(),
    backgroundColor: varchar("background_color", { length: 50 }).notNull(),
    textColor: varchar("text_color", { length: 50 }).notNull(),
    borderColor: varchar("border_color", { length: 50 }).notNull(),
    inputBorderRadius: varchar("input_border_radius", { length: 50 }).notNull(),
    buttonBorderRadius: varchar("button_border_radius", {
      length: 50,
    }).notNull(),
    padding: varchar({ length: 50 }).notNull(),
    buttonTextColor: varchar("button_text_color", { length: 50 }).notNull(),
    buttonAlignment: varchar("button_alignment", { length: 10 }).notNull(),
    font: varchar({ length: 255 }).notNull(),
    fieldBackgroundColor: varchar("field_background_color", {
      length: 50,
    }).notNull(),
    inputTextColor: varchar("input_text_color", { length: 50 }).notNull(),
    formBorderRadius: varchar("form_border_radius", { length: 50 }).notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp("updated_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
    placeholderColor: varchar("placeholder_color", { length: 50 })
      .default("#6b7280")
      .notNull(),
  },
  (table) => [
    check(
      "theme_button_alignment_check",
      sql`(button_alignment)::text = ANY ((ARRAY['left'::character varying, 'right'::character varying])::text[])`,
    ),
  ],
);

export const formField = pgTable(
  "form_field",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    formId: uuid("form_id").notNull(),
    type: fieldType().notNull(),
    label: text(),
    required: boolean().default(false).notNull(),
    width: fieldWidth().default("full").notNull(),
    placeholder: text(),
    order: integer().notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp("updated_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
    hideForEventTypes: eventType("hide_for_event_types")
      .array()
      .default([])
      .notNull(),
    rowBreakAfter: boolean("row_break_after").default(false).notNull(),
  },
  (table) => [
    index("idx_form_field_form_id").using(
      "btree",
      table.formId.asc().nullsLast().op("uuid_ops"),
    ),
    index("idx_form_field_order").using(
      "btree",
      table.order.asc().nullsLast().op("int4_ops"),
    ),
    foreignKey({
      columns: [table.formId],
      foreignColumns: [form.id],
      name: "form_field_form_id_fkey",
    }).onDelete("cascade"),
    unique("form_field_form_id_type_key").on(table.formId, table.type),
  ],
);

export const lead = pgTable(
  "lead",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    propertyId: uuid("property_id").notNull(),
    email: varchar({ length: 255 }),
    firstName: varchar("first_name", { length: 255 }),
    lastName: varchar("last_name", { length: 255 }),
    phone: varchar({ length: 255 }),
    company: varchar({ length: 255 }),
    city: varchar({ length: 255 }),
    state: varchar({ length: 255 }),
    postalCode: varchar("postal_code", { length: 15 }),
    eventType: eventType("event_type"),
    startDate: date("start_date"),
    endDate: date("end_date"),
    guestCount: integer("guest_count"),
    budget: numeric(),
    eventDescription: text("event_description"),
    flexibleDates: boolean("flexible_dates"),
    marketingConsent: boolean("marketing_consent"),
    source: leadSource().notNull(),
    sourceId: uuid("source_id").notNull(),
    highlevelContactId: varchar("highlevel_contact_id", { length: 255 }),
    highlevelOpportunityId: varchar("highlevel_opportunity_id", {
      length: 255,
    }),
    assignedSalesRepEmail: varchar("assigned_sales_rep_email", { length: 255 }),
    assignedAt: timestamp("assigned_at", {
      withTimezone: true,
      mode: "string",
    }),
    eventNeeds: eventNeed("event_needs").array(),
    createdAt: timestamp("created_at", {
      withTimezone: true,
      mode: "string",
    }).default(sql`CURRENT_TIMESTAMP`),
    updatedAt: timestamp("updated_at", { withTimezone: true, mode: "string" }),
    roomCount: integer("room_count"),
    mealCount: integer("meal_count"),
    eventName: varchar("event_name", { length: 255 }),
    country: varchar({ length: 255 }),
  },
  (table) => [
    index("idx_lead_email").using(
      "btree",
      table.email.asc().nullsLast().op("text_ops"),
    ),
    index("idx_lead_highlevel_contact_id")
      .using("btree", table.highlevelContactId.asc().nullsLast().op("text_ops"))
      .where(sql`(highlevel_contact_id IS NOT NULL)`),
    index("idx_lead_source_id").using(
      "btree",
      table.sourceId.asc().nullsLast().op("uuid_ops"),
    ),
    foreignKey({
      columns: [table.propertyId],
      foreignColumns: [property.id],
      name: "lead_property_id_fkey",
    }).onDelete("cascade"),
  ],
);

export const integrationLeadSyncLog = pgTable(
  "integration_lead_sync_log",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    leadId: uuid("lead_id").notNull(),
    integrationType: integrationType("integration_type").notNull(),
    integrationLeadId: varchar("integration_lead_id", {
      length: 255,
    }).notNull(),
    syncedAt: timestamp("synced_at", { withTimezone: true, mode: "string" })
      .defaultNow()
      .notNull(),
  },
  (table) => [
    index("integration_lead_sync_log_integration_lead_id_idx").using(
      "btree",
      table.integrationLeadId.asc().nullsLast().op("text_ops"),
    ),
    index("integration_lead_sync_log_integration_type_idx").using(
      "btree",
      table.integrationType.asc().nullsLast().op("enum_ops"),
    ),
    index("integration_lead_sync_log_lead_id_idx").using(
      "btree",
      table.leadId.asc().nullsLast().op("uuid_ops"),
    ),
    index("integration_lead_sync_log_lead_integration_sync_idx").using(
      "btree",
      table.leadId.asc().nullsLast().op("timestamptz_ops"),
      table.integrationType.asc().nullsLast().op("enum_ops"),
      table.syncedAt.desc().nullsFirst().op("enum_ops"),
    ),
    foreignKey({
      columns: [table.leadId],
      foreignColumns: [lead.id],
      name: "integration_lead_sync_log_lead_id_fkey",
    }).onDelete("cascade"),
  ],
);

export const leadFile = pgTable(
  "lead_file",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    leadId: uuid("lead_id"),
    fileName: varchar("file_name", { length: 255 }).notNull(),
    s3Key: text("s3_key").notNull(),
    contentType: varchar("content_type", { length: 255 }).notNull(),
    createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
      .default(sql`CURRENT_TIMESTAMP`)
      .notNull(),
    updatedAt: timestamp("updated_at", { withTimezone: true, mode: "string" }),
    s3KeyWithoutStatusPrefix: text("s3_key_without_status_prefix").notNull(),
  },
  (table) => [
    index("idx_lead_files_lead_id").using(
      "btree",
      table.leadId.asc().nullsLast().op("uuid_ops"),
    ),
    foreignKey({
      columns: [table.leadId],
      foreignColumns: [lead.id],
      name: "lead_files_lead_id_fkey",
    }).onDelete("cascade"),
  ],
);

export const formFieldOption = pgTable(
  "form_field_option",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    formFieldId: uuid("form_field_id").notNull(),
    optionValue: text("option_value").notNull(),
  },
  (table) => [
    index("idx_form_field_options_form_field_id").using(
      "btree",
      table.formFieldId.asc().nullsLast().op("uuid_ops"),
    ),
    foreignKey({
      columns: [table.formFieldId],
      foreignColumns: [formField.id],
      name: "fk_form_field_options_form_field_id",
    }).onDelete("cascade"),
  ],
);

export const managementGroupApiKey = pgTable(
  "management_group_api_key",
  {
    id: uuid().defaultRandom().primaryKey().notNull(),
    managementGroupId: uuid("management_group_id").notNull(),
    name: varchar({ length: 255 }).notNull(),
    key: varchar({ length: 255 }).notNull(),
    isActive: boolean("is_active").default(true).notNull(),
    createdAt: timestamp("created_at", {
      withTimezone: true,
      mode: "string",
    }).default(sql`CURRENT_TIMESTAMP`),
    updatedAt: timestamp("updated_at", { withTimezone: true, mode: "string" }),
  },
  (table) => [
    index("idx_management_group_api_key_management_group_id").using(
      "btree",
      table.managementGroupId.asc().nullsLast().op("uuid_ops"),
    ),
    foreignKey({
      columns: [table.managementGroupId],
      foreignColumns: [managementGroup.id],
      name: "management_group_api_key_management_group_id_fkey",
    }).onDelete("cascade"),
    unique("management_group_api_key_key_key").on(table.key),
  ],
);

export const apiLeadSubmissions = pgTable("api_lead_submissions", {
  id: uuid().defaultRandom().primaryKey().notNull(),
  createdAt: timestamp("created_at", { withTimezone: true, mode: "string" })
    .defaultNow()
    .notNull(),
  relaxedValidations: boolean("relaxed_validations").default(false).notNull(),
  passedStrictValidations: boolean("passed_strict_validations")
    .default(false)
    .notNull(),
});
