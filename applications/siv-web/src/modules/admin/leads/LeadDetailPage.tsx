import {
  AdminLayout,
  AdminLayoutComponentProps,
} from "@/modules/admin/AdminLayout";
import { lead, property, apiLeadSubmissions } from "@/drizzle/schema";
import { InferModel } from "drizzle-orm";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  Building2,
  Calendar,
  Mail,
  MapPin,
  Phone,
  DollarSign,
  Users,
  Utensils,
  Hotel,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";

type Lead = InferModel<typeof lead>;
type Property = InferModel<typeof property>;
type ApiLeadSubmission = InferModel<typeof apiLeadSubmissions>;

function LeadDetailPage(
  props: AdminLayoutComponentProps & {
    lead: Lead;
    property: Property;
    apiSubmission: ApiLeadSubmission | null;
    leadAdminRoutesBaseUrl: string;
  },
) {
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Not provided";
    try {
      return format(new Date(dateString), "MMMM d, yyyy");
    } catch {
      return "Invalid date";
    }
  };

  const formatDateTime = (dateString: string | null) => {
    if (!dateString) return "Not provided";
    try {
      return format(new Date(dateString), "MMMM d, yyyy 'at' h:mm a");
    } catch {
      return "Invalid date";
    }
  };

  const formatEventType = (eventType: string | null) => {
    if (!eventType) return "Not specified";
    return eventType.charAt(0).toUpperCase() + eventType.slice(1);
  };

  const formatEventNeeds = (needs: string[] | null) => {
    if (!needs || needs.length === 0) return "None specified";
    return needs
      .map((need) =>
        need
          .toLowerCase()
          .replace(/_/g, " ")
          .replace(/\b\w/g, (c) => c.toUpperCase()),
      )
      .join(", ");
  };

  return (
    <AdminLayout
      pagePath={props.pagePath}
      pageTitle={props.pageTitle}
      user={props.user}
      adminPortalBaseUrl={props.adminPortalBaseUrl}
    >
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => window.history.back()}
            className="gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Contact Information Card */}
          <Card>
            <CardHeader>
              <CardTitle>Contact Information</CardTitle>
              <CardDescription>Lead contact details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Name
                </p>
                <p className="text-lg">
                  {props.lead.firstName || props.lead.lastName ? (
                    `${props.lead.firstName || ""} ${props.lead.lastName || ""}`.trim()
                  ) : (
                    <span className="text-muted-foreground">Not provided</span>
                  )}
                </p>
              </div>

              {props.lead.company && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                    <Building2 className="h-3 w-3" /> Company
                  </p>
                  <p className="text-lg">{props.lead.company}</p>
                </div>
              )}

              <div>
                <p className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                  <Mail className="h-3 w-3" /> Email
                </p>
                <p className="text-lg">
                  {props.lead.email || (
                    <span className="text-muted-foreground">Not provided</span>
                  )}
                </p>
              </div>

              <div>
                <p className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                  <Phone className="h-3 w-3" /> Phone
                </p>
                <p className="text-lg">
                  {props.lead.phone || (
                    <span className="text-muted-foreground">Not provided</span>
                  )}
                </p>
              </div>

              <div>
                <p className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                  <MapPin className="h-3 w-3" /> Location
                </p>
                <p className="text-lg">
                  {[
                    props.lead.city,
                    props.lead.state,
                    props.lead.postalCode,
                    props.lead.country,
                  ]
                    .filter(Boolean)
                    .join(", ") || (
                    <span className="text-muted-foreground">Not provided</span>
                  )}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Event Details Card */}
          <Card>
            <CardHeader>
              <CardTitle>Event Details</CardTitle>
              <CardDescription>
                Event information provided by the lead
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Event Type
                </p>
                <Badge variant="secondary" className="mt-1">
                  {formatEventType(props.lead.eventType)}
                </Badge>
              </div>

              {props.lead.eventName && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Event Name
                  </p>
                  <p className="text-lg">{props.lead.eventName}</p>
                </div>
              )}

              <div>
                <p className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                  <Calendar className="h-3 w-3" /> Event Dates
                </p>
                <p className="text-lg">
                  {props.lead.startDate ? (
                    <>
                      {formatDate(props.lead.startDate)}
                      {props.lead.endDate &&
                        props.lead.endDate !== props.lead.startDate && (
                          <> - {formatDate(props.lead.endDate)}</>
                        )}
                    </>
                  ) : (
                    <span className="text-muted-foreground">Not provided</span>
                  )}
                </p>
                {props.lead.flexibleDates && (
                  <Badge variant="outline" className="mt-1">
                    Flexible dates
                  </Badge>
                )}
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                    <Users className="h-3 w-3" /> Guests
                  </p>
                  <p className="text-lg">{props.lead.guestCount || "-"}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                    <Hotel className="h-3 w-3" /> Rooms
                  </p>
                  <p className="text-lg">{props.lead.roomCount || "-"}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                    <Utensils className="h-3 w-3" /> Meals
                  </p>
                  <p className="text-lg">{props.lead.mealCount || "-"}</p>
                </div>
              </div>

              <div>
                <p className="text-sm font-medium text-muted-foreground flex items-center gap-1">
                  <DollarSign className="h-3 w-3" /> Budget
                </p>
                <p className="text-lg">
                  {props.lead.budget ? (
                    `$${Number(props.lead.budget).toLocaleString()}`
                  ) : (
                    <span className="text-muted-foreground">Not provided</span>
                  )}
                </p>
              </div>

              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Event Needs
                </p>
                <p className="text-lg">
                  {formatEventNeeds(props.lead.eventNeeds)}
                </p>
              </div>

              {props.lead.eventDescription && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Description
                  </p>
                  <p className="text-base mt-1 whitespace-pre-wrap">
                    {props.lead.eventDescription}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Submission Details Card */}
        <Card>
          <CardHeader>
            <CardTitle>Submission Details</CardTitle>
            <CardDescription>
              Information about how this lead was submitted
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Property
                </p>
                <p className="text-lg">
                  <a
                    href={`/admin/properties/${props.property.id}`}
                    className="text-blue-600 hover:underline"
                  >
                    {props.property.name}
                  </a>
                </p>
              </div>

              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Submission Method
                </p>
                <p className="text-lg">
                  <Badge>API</Badge>
                </p>
              </div>

              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Submitted At
                </p>
                <p className="text-lg">
                  {formatDateTime(props.lead.createdAt)}
                </p>
              </div>

              {props.apiSubmission && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    Validation Mode
                  </p>
                  <p className="text-lg">
                    {props.apiSubmission.relaxedValidations ? (
                      <Badge variant="outline">Relaxed</Badge>
                    ) : (
                      <Badge variant="default">Strict</Badge>
                    )}
                    {props.apiSubmission.passedStrictValidations === false && (
                      <Badge variant="destructive" className="ml-2">
                        Failed strict validation
                      </Badge>
                    )}
                  </p>
                </div>
              )}

              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Marketing Consent
                </p>
                <p className="text-lg">
                  {props.lead.marketingConsent ? (
                    <Badge variant="default">Yes</Badge>
                  ) : (
                    <Badge variant="secondary">No</Badge>
                  )}
                </p>
              </div>

              {props.lead.highlevelContactId && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    HighLevel Contact ID
                  </p>
                  <p className="text-lg font-mono text-sm">
                    {props.lead.highlevelContactId}
                  </p>
                </div>
              )}

              {props.lead.highlevelOpportunityId && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    HighLevel Opportunity ID
                  </p>
                  <p className="text-lg font-mono text-sm">
                    {props.lead.highlevelOpportunityId}
                  </p>
                </div>
              )}

              {props.lead.assignedSalesRepEmail && (
                <>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Assigned Sales Rep
                    </p>
                    <p className="text-lg">
                      {props.lead.assignedSalesRepEmail}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Assigned At
                    </p>
                    <p className="text-lg">
                      {formatDateTime(props.lead.assignedAt)}
                    </p>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}

export default LeadDetailPage;
