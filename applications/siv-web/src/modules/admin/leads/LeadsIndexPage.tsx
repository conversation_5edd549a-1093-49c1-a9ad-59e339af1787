import {
  AdminLayout,
  AdminLayoutComponentProps,
} from "@/modules/admin/AdminLayout";
import { lead, property } from "@/drizzle/schema";
import { InferModel } from "drizzle-orm";
import { hc } from "hono/client";
import type { LeadRoutes } from "@/modules/admin/leads/lead-routes";
import { Button } from "@/components/ui/button";
import { Eye, Trash2, Calendar, Mail, Phone, Building2 } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from "@/components/ui/pagination";
import { PaginationMetadata } from "@/pagination/pagination-utils";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

type Lead = InferModel<typeof lead>;
type Property = InferModel<typeof property>;

function LeadsIndexPage(
  props: AdminLayoutComponentProps & {
    leads: Lead[];
    properties: Property[];
    selectedProperty: Property | null;
    selectedSource: string;
    leadRoutesBaseUrl: string;
    paginationMetadata?: PaginationMetadata;
  },
) {
  // Create a type-safe client for lead routes
  const client = hc<LeadRoutes>(props.leadRoutesBaseUrl);

  // Create a map of property id to property for quick lookup
  const propertyMap = new Map(props.properties.map((p) => [p.id, p]));

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "-";
    try {
      return format(new Date(dateString), "MMM d, yyyy");
    } catch {
      return "-";
    }
  };

  const formatEventType = (eventType: string | null) => {
    if (!eventType) return "-";
    return eventType.charAt(0).toUpperCase() + eventType.slice(1);
  };

  return (
    <AdminLayout
      pagePath={props.pagePath}
      pageTitle={props.pageTitle}
      user={props.user}
      adminPortalBaseUrl={props.adminPortalBaseUrl}
    >
      <div className="space-y-4">
        {props.selectedProperty && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-sm text-blue-800">
              Showing leads for property:{" "}
              <strong>{props.selectedProperty.name}</strong>
            </p>
          </div>
        )}

        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">Source:</span>
            <Select
              value={props.selectedSource}
              onValueChange={(value) => {
                const params = new URLSearchParams();
                params.set("page", "1"); // Reset to first page when changing filter
                if (props.selectedProperty?.id) {
                  params.set("propertyId", props.selectedProperty.id);
                }
                if (value !== "all") {
                  params.set("source", value);
                }
                if (
                  props.paginationMetadata &&
                  props.paginationMetadata.pageSize !== 10
                ) {
                  params.set(
                    "pageSize",
                    props.paginationMetadata.pageSize.toString(),
                  );
                }
                window.location.href = `?${params.toString()}`;
              }}
            >
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Sources</SelectItem>
                <SelectItem value="api">API</SelectItem>
                <SelectItem value="form_submission">Form Submission</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="bg-white py-4 rounded-md">
          <div className="overflow-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>NAME</TableHead>
                  <TableHead>CONTACT</TableHead>
                  <TableHead>PROPERTY</TableHead>
                  <TableHead>EVENT TYPE</TableHead>
                  <TableHead>EVENT DATE</TableHead>
                  <TableHead>SUBMITTED</TableHead>
                  <TableHead className="text-center">ACTIONS</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {props.leads.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={7}
                      className="text-center text-muted-foreground"
                      data-testid="no-leads-message"
                    >
                      No leads found
                      {props.selectedProperty
                        ? ` for ${props.selectedProperty.name}`
                        : ""}
                    </TableCell>
                  </TableRow>
                ) : (
                  props.leads.map((leadItem) => {
                    const leadProperty = propertyMap.get(leadItem.propertyId);
                    return (
                      <TableRow
                        key={leadItem.id}
                        id={`leads-row-${leadItem.id}`}
                      >
                        <TableCell
                          id={`leads-cell-name-${leadItem.id}`}
                          data-testid="lead-name"
                        >
                          <div>
                            <div className="font-medium">
                              {leadItem.firstName || leadItem.lastName ? (
                                `${leadItem.firstName || ""} ${leadItem.lastName || ""}`.trim()
                              ) : (
                                <span className="text-muted-foreground">
                                  No name
                                </span>
                              )}
                            </div>
                            {leadItem.company && (
                              <div className="text-sm text-muted-foreground flex items-center gap-1">
                                <Building2 className="h-3 w-3" />
                                {leadItem.company}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell
                          id={`leads-cell-contact-${leadItem.id}`}
                          data-testid="lead-contact"
                        >
                          <div className="space-y-1">
                            {leadItem.email && (
                              <div className="text-sm flex items-center gap-1">
                                <Mail className="h-3 w-3 text-muted-foreground" />
                                {leadItem.email}
                              </div>
                            )}
                            {leadItem.phone && (
                              <div className="text-sm flex items-center gap-1">
                                <Phone className="h-3 w-3 text-muted-foreground" />
                                {leadItem.phone}
                              </div>
                            )}
                            {!leadItem.email && !leadItem.phone && (
                              <span className="text-muted-foreground text-sm">
                                No contact info
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell
                          id={`leads-cell-property-${leadItem.id}`}
                          data-testid="lead-property"
                        >
                          {leadProperty ? (
                            <a
                              href={`/admin/properties/${leadProperty.id}`}
                              className="text-blue-600 hover:underline"
                            >
                              {leadProperty.name}
                            </a>
                          ) : (
                            <span className="text-muted-foreground">
                              Unknown
                            </span>
                          )}
                        </TableCell>
                        <TableCell
                          id={`leads-cell-event-type-${leadItem.id}`}
                          data-testid="lead-event-type"
                        >
                          {leadItem.eventType ? (
                            <Badge variant="secondary">
                              {formatEventType(leadItem.eventType)}
                            </Badge>
                          ) : (
                            "-"
                          )}
                        </TableCell>
                        <TableCell
                          id={`leads-cell-event-date-${leadItem.id}`}
                          data-testid="lead-event-date"
                        >
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3 text-muted-foreground" />
                            {formatDate(leadItem.startDate)}
                          </div>
                        </TableCell>
                        <TableCell
                          id={`leads-cell-submitted-${leadItem.id}`}
                          data-testid="lead-submitted"
                        >
                          {formatDate(leadItem.createdAt)}
                        </TableCell>
                        <TableCell
                          id={`leads-cell-actions-${leadItem.id}`}
                          className="text-center"
                        >
                          <div className="flex justify-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                (window.location.href = client[":id"].$url({
                                  param: { id: leadItem.id },
                                }).href)
                              }
                              aria-label={`View details for lead`}
                              data-testid={`lead-view-${leadItem.id}`}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={async () => {
                                if (
                                  confirm(
                                    "Are you sure you want to delete this lead?",
                                  )
                                ) {
                                  const res = await fetch(
                                    client[":id"].$url({
                                      param: { id: leadItem.id },
                                    }).href,
                                    { method: "DELETE" },
                                  );
                                  if (res.ok) {
                                    window.location.reload();
                                  }
                                }
                              }}
                              aria-label={`Delete lead`}
                              data-testid={`lead-delete-${leadItem.id}`}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {props.paginationMetadata && (
            <div className="mt-4 space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">Show</span>
                  <Select
                    value={props.paginationMetadata.pageSize.toString()}
                    onValueChange={(value) => {
                      const params = new URLSearchParams();
                      params.set("page", "1"); // Reset to first page when changing page size
                      params.set("pageSize", value);
                      if (props.selectedProperty?.id) {
                        params.set("propertyId", props.selectedProperty.id);
                      }
                      if (
                        props.selectedSource &&
                        props.selectedSource !== "all"
                      ) {
                        params.set("source", props.selectedSource);
                      }
                      window.location.href = `?${params.toString()}`;
                    }}
                  >
                    <SelectTrigger className="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="25">25</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                    </SelectContent>
                  </Select>
                  <span className="text-sm text-muted-foreground">entries</span>
                </div>

                {props.paginationMetadata.totalPages > 1 && (
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          href={buildPaginationUrl(
                            props.paginationMetadata.currentPage - 1,
                            props.selectedProperty?.id,
                            props.paginationMetadata.pageSize,
                            props.selectedSource,
                          )}
                          className={
                            props.paginationMetadata.currentPage === 1
                              ? "pointer-events-none opacity-50"
                              : ""
                          }
                        />
                      </PaginationItem>

                      {renderPaginationItems(
                        props.paginationMetadata,
                        props.selectedProperty?.id,
                        props.selectedSource,
                      )}

                      <PaginationItem>
                        <PaginationNext
                          href={buildPaginationUrl(
                            props.paginationMetadata.currentPage + 1,
                            props.selectedProperty?.id,
                            props.paginationMetadata.pageSize,
                            props.selectedSource,
                          )}
                          className={
                            props.paginationMetadata.currentPage ===
                            props.paginationMetadata.totalPages
                              ? "pointer-events-none opacity-50"
                              : ""
                          }
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                )}
              </div>

              <div className="text-center text-sm text-muted-foreground">
                Showing{" "}
                {props.paginationMetadata.totalCount === 0
                  ? 0
                  : (props.paginationMetadata.currentPage - 1) *
                      props.paginationMetadata.pageSize +
                    1}{" "}
                to{" "}
                {Math.min(
                  props.paginationMetadata.currentPage *
                    props.paginationMetadata.pageSize,
                  props.paginationMetadata.totalCount,
                )}{" "}
                of {props.paginationMetadata.totalCount} leads
              </div>
            </div>
          )}
        </div>
      </div>
    </AdminLayout>
  );
}

// Helper function to build pagination URLs
function buildPaginationUrl(
  page: number,
  propertyId?: string,
  pageSize?: number,
  source?: string,
): string {
  const params = new URLSearchParams();
  params.set("page", page.toString());
  if (propertyId) {
    params.set("propertyId", propertyId);
  }
  if (pageSize && pageSize !== 10) {
    // Only include pageSize if it's not the default
    params.set("pageSize", pageSize.toString());
  }
  if (source && source !== "all") {
    params.set("source", source);
  }
  return `?${params.toString()}`;
}

// Helper function to render pagination items
function renderPaginationItems(
  metadata: PaginationMetadata,
  propertyId?: string,
  source?: string,
) {
  const items = [];
  const { currentPage, totalPages, pageSize } = metadata;

  // Always show first page
  items.push(
    <PaginationItem key={1}>
      <PaginationLink
        href={buildPaginationUrl(1, propertyId, pageSize, source)}
        isActive={currentPage === 1}
      >
        1
      </PaginationLink>
    </PaginationItem>,
  );

  // Show ellipsis if needed
  if (currentPage > 3) {
    items.push(
      <PaginationItem key="ellipsis-1">
        <PaginationEllipsis />
      </PaginationItem>,
    );
  }

  // Show pages around current page
  for (
    let i = Math.max(2, currentPage - 1);
    i <= Math.min(totalPages - 1, currentPage + 1);
    i++
  ) {
    items.push(
      <PaginationItem key={i}>
        <PaginationLink
          href={buildPaginationUrl(i, propertyId, pageSize, source)}
          isActive={currentPage === i}
        >
          {i}
        </PaginationLink>
      </PaginationItem>,
    );
  }

  // Show ellipsis if needed
  if (currentPage < totalPages - 2) {
    items.push(
      <PaginationItem key="ellipsis-2">
        <PaginationEllipsis />
      </PaginationItem>,
    );
  }

  // Always show last page if there's more than one page
  if (totalPages > 1) {
    items.push(
      <PaginationItem key={totalPages}>
        <PaginationLink
          href={buildPaginationUrl(totalPages, propertyId, pageSize, source)}
          isActive={currentPage === totalPages}
        >
          {totalPages}
        </PaginationLink>
      </PaginationItem>,
    );
  }

  return items;
}

export default LeadsIndexPage;
