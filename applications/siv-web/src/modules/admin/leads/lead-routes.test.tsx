import { describe, expect, it, beforeEach } from "vitest";
import { createLeadRoutes } from "./lead-routes";
import { Hono } from "hono";
import "@testing-library/jest-dom/matchers";
import { Variables } from "@/auth";
import { db } from "@/db/dbclient";
import {
  property,
  lead,
  apiLeadSubmissions,
  highlevelWebhookIntegrationSettings,
} from "@/drizzle/schema";
import { mockAuthMiddleware, testRenderer } from "@/test/setup";
import { eq } from "drizzle-orm";
import { getByTestId, getByText, queryByTestId } from "@testing-library/dom";
import { parseHTML } from "@/test/test-utils";
import { propertyFactory } from "@/modules/admin/properties/property-test-factories";
import { withTransactionalTests } from "@/test/transaction-test-helpers";
import { ModuleBaseUrlFactory } from "@/modules/route-helpers";
import { v4 as uuidv4 } from "uuid";

describe("Admin Leads Route", () => {
  withTransactionalTests();

  function buildTestSetup() {
    const app = new Hono<{ Variables: Variables }>();
    const adminPortalBaseUrlFactory = new ModuleBaseUrlFactory(
      "http://localhost:8080/admin",
    );

    const routes = createLeadRoutes({
      adminPortalBaseUrlFactory: adminPortalBaseUrlFactory as any,
      leadAdminRoutesBaseUrlFactory:
        adminPortalBaseUrlFactory.factoryForNestedModule("/leads") as any,
    });

    app.use("*", mockAuthMiddleware);
    app.use("*", testRenderer);
    app.route("/admin/leads", routes);

    return { app };
  }

  describe("GET /leads", () => {
    it("should render leads index page with API leads and pagination", async () => {
      const { app } = buildTestSetup();

      // Create test property
      const testProperty = await propertyFactory.create({
        name: "Test Property",
        country: "United States",
        region: "Test Region",
        city: "Test City",
        addressLine1: "123 Test St",
        postalCode: "12345",
      });

      await db.insert(highlevelWebhookIntegrationSettings).values({
        propertyId: testProperty.id!!,
        webhookSecret: "test-webhook-secret",
      });

      // Create API submission record
      const [apiSubmission] = await db
        .insert(apiLeadSubmissions)
        .values({
          relaxedValidations: false,
          passedStrictValidations: true,
        })
        .returning();

      // Create test lead with API source
      const [testLead] = await db
        .insert(lead)
        .values({
          propertyId: testProperty.id!!,
          email: "<EMAIL>",
          firstName: "John",
          lastName: "Doe",
          phone: "+1234567890",
          company: "Test Company",
          city: "Lead City",
          state: "CA",
          postalCode: "90210",
          country: "US",
          eventType: "wedding",
          startDate: "2024-12-25",
          endDate: "2024-12-27",
          guestCount: 150,
          roomCount: 50,
          budget: "25000",
          source: "api",
          sourceId: apiSubmission.id,
        })
        .returning();

      const res = await app.request("/admin/leads");
      expect(res.status).toBe(200);

      const document = parseHTML(await res.text());
      expect(getByTestId(document, "lead-name")).toHaveTextContent("John Doe");
      expect(getByTestId(document, "lead-contact")).toHaveTextContent(
        "<EMAIL>",
      );
      expect(getByTestId(document, "lead-contact")).toHaveTextContent(
        "+1234567890",
      );
      expect(getByTestId(document, "lead-property")).toHaveTextContent(
        "Test Property",
      );
      expect(getByTestId(document, "lead-event-type")).toHaveTextContent(
        "Wedding",
      );
    });

    it("should filter leads by property when propertyId query param is provided", async () => {
      const { app } = buildTestSetup();

      // Create two test properties
      const property1 = await propertyFactory.create({
        name: "Property 1",
      });
      const property2 = await propertyFactory.create({
        name: "Property 2",
      });

      // Create API submissions
      const [apiSubmission1] = await db
        .insert(apiLeadSubmissions)
        .values({
          relaxedValidations: false,
          passedStrictValidations: true,
        })
        .returning();

      const [apiSubmission2] = await db
        .insert(apiLeadSubmissions)
        .values({
          relaxedValidations: false,
          passedStrictValidations: true,
        })
        .returning();

      // Create leads for each property
      await db.insert(lead).values([
        {
          propertyId: property1.id!!,
          email: "<EMAIL>",
          firstName: "Lead",
          lastName: "One",
          eventType: "wedding",
          source: "api",
          sourceId: apiSubmission1.id,
        },
        {
          propertyId: property2.id!!,
          email: "<EMAIL>",
          firstName: "Lead",
          lastName: "Two",
          eventType: "corporate_meeting",
          source: "api",
          sourceId: apiSubmission2.id,
        },
      ]);

      // Request with propertyId filter
      const res = await app.request(`/admin/leads?propertyId=${property1.id}`);
      expect(res.status).toBe(200);

      const document = parseHTML(await res.text());
      expect(getByText(document, "Lead One")).toBeInTheDocument();
      expect(queryByTestId(document, "lead-name")?.textContent).not.toContain(
        "Lead Two",
      );
      // Check for the info box that shows the property filter
      const infoBox = document.querySelector(".bg-blue-50");
      expect(infoBox).toBeTruthy();
      expect(infoBox?.textContent).toContain("Showing leads for property:");
      expect(infoBox?.textContent).toContain("Property 1");
    });

    it("should handle empty lead list", async () => {
      const { app } = buildTestSetup();
      await db.delete(lead);

      const res = await app.request("/admin/leads");
      expect(res.status).toBe(200);

      const document = parseHTML(await res.text());
      expect(getByTestId(document, "no-leads-message")).toHaveTextContent(
        "No leads found",
      );
    });

    it("should show all leads by default", async () => {
      const { app } = buildTestSetup();

      const testProperty = await propertyFactory.create({
        name: "Test Property",
      });

      // Create API submission
      const [apiSubmission] = await db
        .insert(apiLeadSubmissions)
        .values({
          relaxedValidations: false,
          passedStrictValidations: true,
        })
        .returning();

      // Create leads with different sources
      await db.insert(lead).values([
        {
          propertyId: testProperty.id!!,
          email: "<EMAIL>",
          firstName: "API",
          lastName: "Lead",
          eventType: "wedding",
          source: "api",
          sourceId: apiSubmission.id,
        },
        {
          propertyId: testProperty.id!!,
          email: "<EMAIL>",
          firstName: "Form",
          lastName: "Lead",
          eventType: "wedding",
          source: "form_submission",
          sourceId: uuidv4(),
        },
      ]);

      const res = await app.request("/admin/leads");
      expect(res.status).toBe(200);

      const document = parseHTML(await res.text());
      expect(getByText(document, "API Lead")).toBeInTheDocument();
      expect(getByText(document, "Form Lead")).toBeInTheDocument();
    });

    it("should filter leads by source when source query param is provided", async () => {
      const { app } = buildTestSetup();

      const testProperty = await propertyFactory.create({
        name: "Test Property",
      });

      // Create API submission
      const [apiSubmission] = await db
        .insert(apiLeadSubmissions)
        .values({
          relaxedValidations: false,
          passedStrictValidations: true,
        })
        .returning();

      // Create leads with different sources
      await db.insert(lead).values([
        {
          propertyId: testProperty.id!!,
          email: "<EMAIL>",
          firstName: "API",
          lastName: "Lead",
          eventType: "wedding",
          source: "api",
          sourceId: apiSubmission.id,
        },
        {
          propertyId: testProperty.id!!,
          email: "<EMAIL>",
          firstName: "Form",
          lastName: "Lead",
          eventType: "wedding",
          source: "form_submission",
          sourceId: uuidv4(),
        },
      ]);

      // Test filtering by API source
      const res = await app.request("/admin/leads?source=api");
      expect(res.status).toBe(200);

      const document = parseHTML(await res.text());
      expect(getByText(document, "API Lead")).toBeInTheDocument();
      expect(queryByTestId(document, "lead-name")?.textContent).not.toContain(
        "Form Lead",
      );
    });

    it("should handle pagination correctly", async () => {
      const { app } = buildTestSetup();

      const testProperty = await propertyFactory.create({
        name: "Test Property",
      });

      // Create multiple API submissions and leads (more than default page size)
      for (let i = 0; i < 15; i++) {
        const [apiSubmission] = await db
          .insert(apiLeadSubmissions)
          .values({
            relaxedValidations: false,
            passedStrictValidations: true,
          })
          .returning();

        await db.insert(lead).values({
          propertyId: testProperty.id!!,
          email: `lead${i}@example.com`,
          firstName: `Lead`,
          lastName: `${i}`,
          eventType: "wedding",
          source: "api",
          sourceId: apiSubmission.id,
        });
      }

      // Test first page
      const res1 = await app.request("/admin/leads?page=1");
      expect(res1.status).toBe(200);

      const document1 = parseHTML(await res1.text());
      const rows1 = document1.querySelectorAll('[data-testid="lead-name"]');
      expect(rows1.length).toBe(10); // Default page size

      // Check pagination info is shown
      expect(
        getByText(document1, "Showing 1 to 10 of 15 leads"),
      ).toBeInTheDocument();

      // Test second page
      const res2 = await app.request("/admin/leads?page=2");
      expect(res2.status).toBe(200);

      const document2 = parseHTML(await res2.text());
      const rows2 = document2.querySelectorAll('[data-testid="lead-name"]');
      expect(rows2.length).toBe(5); // Remaining leads

      // Check pagination info for second page
      expect(
        getByText(document2, "Showing 11 to 15 of 15 leads"),
      ).toBeInTheDocument();
    });
  });

  describe("GET /leads/:id", () => {
    it("should render lead detail page", async () => {
      const { app } = buildTestSetup();

      const testProperty = await propertyFactory.create({
        name: "Test Property",
      });

      // Create API submission with validation info
      const [apiSubmission] = await db
        .insert(apiLeadSubmissions)
        .values({
          relaxedValidations: true,
          passedStrictValidations: false,
        })
        .returning();

      // Create test lead
      const [testLead] = await db
        .insert(lead)
        .values({
          propertyId: testProperty.id!!,
          email: "<EMAIL>",
          firstName: "John",
          lastName: "Doe",
          phone: "+1234567890",
          company: "Test Company",
          city: "Lead City",
          state: "CA",
          postalCode: "90210",
          country: "US",
          eventType: "wedding",
          eventName: "Smith-Johnson Wedding",
          startDate: "2024-12-25",
          endDate: "2024-12-27",
          guestCount: 150,
          roomCount: 50,
          mealCount: 300,
          budget: "25000",
          flexibleDates: true,
          eventNeeds: ["WEDDING_CEREMONY", "WEDDING_RECEPTION"],
          eventDescription: "Looking for a beautiful venue",
          marketingConsent: true,
          source: "api",
          sourceId: apiSubmission.id,
        })
        .returning();

      const res = await app.request(`/admin/leads/${testLead.id}`);
      expect(res.status).toBe(200);

      const document = parseHTML(await res.text());

      // Check contact information
      expect(getByText(document, "John Doe")).toBeInTheDocument();
      expect(getByText(document, "Test Company")).toBeInTheDocument();
      expect(getByText(document, "<EMAIL>")).toBeInTheDocument();
      expect(getByText(document, "+1234567890")).toBeInTheDocument();

      // Check event details
      expect(getByText(document, "Smith-Johnson Wedding")).toBeInTheDocument();
      expect(getByText(document, "150")).toBeInTheDocument(); // guests
      expect(getByText(document, "50")).toBeInTheDocument(); // rooms
      expect(getByText(document, "$25,000")).toBeInTheDocument(); // budget

      // Check submission details
      expect(getByText(document, "Test Property")).toBeInTheDocument();
      expect(getByText(document, "Relaxed")).toBeInTheDocument(); // validation mode
      expect(
        getByText(document, "Failed strict validation"),
      ).toBeInTheDocument();
    });

    it("should redirect to 404 for non-existent lead", async () => {
      const { app } = buildTestSetup();
      const nonExistentId = uuidv4();

      const res = await app.request(`/admin/leads/${nonExistentId}`);
      expect(res.status).toBe(302);
      expect(res.headers.get("location")).toBe("/404");
    });
  });

  describe("DELETE /leads/:id", () => {
    it("should delete lead and redirect to index", async () => {
      const { app } = buildTestSetup();

      const testProperty = await propertyFactory.create({
        name: "Test Property",
      });

      const [apiSubmission] = await db
        .insert(apiLeadSubmissions)
        .values({
          relaxedValidations: false,
          passedStrictValidations: true,
        })
        .returning();

      const [testLead] = await db
        .insert(lead)
        .values({
          propertyId: testProperty.id!!,
          email: "<EMAIL>",
          firstName: "John",
          lastName: "Doe",
          eventType: "wedding",
          source: "api",
          sourceId: apiSubmission.id,
        })
        .returning();

      const res = await app.request(`/admin/leads/${testLead.id}`, {
        method: "DELETE",
      });

      expect(res.status).toBe(302);
      expect(res.headers.get("location")).toBe("/admin/leads");

      // Verify lead was deleted
      const deletedLead = await db.query.lead.findFirst({
        where: eq(lead.id, testLead.id),
      });
      expect(deletedLead).toBeUndefined();
    });

    it("should redirect to 404 for non-existent lead", async () => {
      const { app } = buildTestSetup();
      const nonExistentId = uuidv4();

      const res = await app.request(`/admin/leads/${nonExistentId}`, {
        method: "DELETE",
      });

      expect(res.status).toBe(302);
      expect(res.headers.get("location")).toBe("/404");
    });
  });
});
