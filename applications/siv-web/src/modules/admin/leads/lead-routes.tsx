import { ModuleBaseUrlFactory } from "@/modules/route-helpers";
import { <PERSON><PERSON> } from "hono";
import { Variables } from "@/auth";
import { db } from "@/db/dbclient";
import {
  lead as leadTable,
  property as propertyTable,
  apiLeadSubmissions,
} from "@/drizzle/schema";
import React from "react";
import LeadsIndexPage from "@/modules/admin/leads/LeadsIndexPage";
import LeadDetailPage from "@/modules/admin/leads/LeadDetailPage";
import { eq, and, desc, sql } from "drizzle-orm";
import logger from "@/logger";
import {
  getPaginationParams,
  getPaginationMetadata,
} from "@/pagination/pagination-utils";
import { Lead } from "@/modules/leads/domain/types";

type CreateLeadRoutesParams = {
  adminPortalBaseUrlFactory: ModuleBaseUrlFactory;
  leadAdminRoutesBaseUrlFactory: ModuleBaseUrlFactory;
};

export function createLeadRoutes(routeParams: CreateLeadRoutesParams) {
  const leadRoutes = new Hono<{ Variables: Variables }>()
    .get("/", async (c) => {
      const propertyId = c.req.query("propertyId");
      const sourceParam = c.req.query("source") || "all";
      function isValidLeadSource(s: string): s is Lead["source"] {
        return ["form_submission", "api"].includes(s);
      }
      const source = sourceParam; // type: string
      // End of Selection

      const paginationParams = getPaginationParams(c);

      try {
        // Build where conditions based on filters
        const whereConditions = [];
        if (propertyId) {
          whereConditions.push(eq(leadTable.propertyId, propertyId));
        }
        if (source !== "all") {
          whereConditions.push(eq(leadTable.source, source as Lead["source"]));
        }
        const whereClause =
          whereConditions.length > 0 ? and(...whereConditions) : undefined;

        // First, get the total count for pagination
        const countQuery = db
          .select({ count: sql<string>`count(*)::int` })
          .from(leadTable)
          .innerJoin(propertyTable, eq(leadTable.propertyId, propertyTable.id));

        if (whereClause) {
          countQuery.where(whereClause);
        }

        const [countResult] = await countQuery;
        const totalCount = Number(countResult?.count || 0);

        // Build query with pagination
        const query = db
          .select({
            lead: leadTable,
            property: propertyTable,
          })
          .from(leadTable)
          .innerJoin(propertyTable, eq(leadTable.propertyId, propertyTable.id))
          .orderBy(desc(leadTable.createdAt))
          .limit(paginationParams.pageSize)
          .offset(paginationParams.offset);

        if (whereClause) {
          query.where(whereClause);
        }

        const results = (await query) || [];
        const paginationMetadata = getPaginationMetadata(
          totalCount,
          paginationParams,
        );

        // If propertyId provided, get property details
        let selectedProperty = null;
        if (propertyId) {
          [selectedProperty] = await db
            .select()
            .from(propertyTable)
            .where(eq(propertyTable.id, propertyId))
            .limit(1);
        }

        return c.render(
          <LeadsIndexPage
            pagePath={c.req.path}
            leads={results.map((r) => r.lead)}
            properties={results.map((r) => r.property)}
            selectedProperty={selectedProperty}
            selectedSource={source}
            paginationMetadata={paginationMetadata}
            adminPortalBaseUrl={routeParams.adminPortalBaseUrlFactory.baseUrl(
              c.req,
            )}
            leadRoutesBaseUrl={routeParams.leadAdminRoutesBaseUrlFactory.baseUrl(
              c.req,
            )}
            user={c.get("user")!!}
            pageTitle={
              selectedProperty ? `Leads - ${selectedProperty.name}` : "Leads"
            }
          />,
        );
      } catch (error) {
        logger.error("Failed to fetch API leads", {
          error: error instanceof Error ? error.message : "Unknown error",
          propertyId,
        });
        return c.text("Failed to fetch API leads", 500);
      }
    })
    .get("/:id", async (c) => {
      const id = c.req.param("id");

      // Get lead with property and API submission details
      const [leadData] = await db
        .select({
          lead: leadTable,
          property: propertyTable,
          apiSubmission: apiLeadSubmissions,
        })
        .from(leadTable)
        .innerJoin(propertyTable, eq(leadTable.propertyId, propertyTable.id))
        .leftJoin(
          apiLeadSubmissions,
          eq(leadTable.sourceId, apiLeadSubmissions.id),
        )
        .where(eq(leadTable.id, id))
        .limit(1);

      if (!leadData) {
        return c.redirect("/404");
      }

      return c.render(
        <LeadDetailPage
          pagePath={c.req.path}
          lead={leadData.lead}
          property={leadData.property}
          apiSubmission={leadData.apiSubmission}
          adminPortalBaseUrl={routeParams.adminPortalBaseUrlFactory.baseUrl(
            c.req,
          )}
          leadAdminRoutesBaseUrl={routeParams.leadAdminRoutesBaseUrlFactory.baseUrl(
            c.req,
          )}
          user={c.get("user")!!}
          pageTitle={`Lead: ${leadData.lead.firstName || ""} ${leadData.lead.lastName || ""}`}
        />,
      );
    })
    .delete("/:id", async (c) => {
      const id = c.req.param("id");

      // Check if lead exists first
      const existingLead = await db.query.lead.findFirst({
        where: eq(leadTable.id, id),
      });

      if (!existingLead) {
        return c.redirect("/404");
      }

      try {
        await db.delete(leadTable).where(eq(leadTable.id, id));
        return c.redirect("/admin/leads");
      } catch (error) {
        logger.error("Failed to delete lead", {
          leadId: id,
          error: error instanceof Error ? error.message : "Unknown error",
        });
        return c.text("Failed to delete lead", 500);
      }
    });

  return leadRoutes;
}

export type LeadRoutes = ReturnType<typeof createLeadRoutes>;
