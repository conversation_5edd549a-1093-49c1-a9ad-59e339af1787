import { beforeEach, describe, expect, it, vi } from "vitest";
import { HighlevelSyncApplicationService } from "./HighlevelSyncApplicationService";
import { db } from "@/db/dbclient";
import { propertyFactory } from "@/modules/admin/properties/property-test-factories";
import { mock, MockProxy } from "vitest-mock-extended";
import {
  HighLevelClient,
  HighLevelCreateOpportunityParams,
} from "@siv/highlevel-client";
import { err, ok } from "neverthrow";
import { withTransactionalTests } from "@/test/transaction-test-helpers";
import { leadFactory } from "@/modules/leads/lead-test-factories";
import { formSubmission, lead } from "@/drizzle/schema";
import { formFactory } from "@/modules/admin/forms/form-test-factories";
import { eq } from "drizzle-orm";

describe("HighlevelSyncApplicationService", () => {
  withTransactionalTests();

  let service: HighlevelSyncApplicationService;
  let mockHighLevelClient: MockProxy<HighLevelClient>;

  beforeEach(() => {
    mockHighLevelClient = mock<HighLevelClient>();
    service = new HighlevelSyncApplicationService(mockHighLevelClient);
    vi.resetAllMocks();
  });

  describe("creating contacts in highlevel", () => {
    it("successfully syncs a lead to HighLevel and only updates that specific lead", async () => {
      // Create test property
      const property = await propertyFactory.create({
        leadAssignmentIntegrationToken: "test-token",
        leadAssignmentIntegrationLocationId: "test-location",
      });

      // Create multiple test leads to ensure isolation
      const otherLead1 = await leadFactory.create({
        propertyId: property.id,
        highlevelContactId: null,
      });

      const testLead = await leadFactory.create({
        propertyId: property.id,
        email: "<EMAIL>",
        firstName: "Test",
        lastName: "User",
        source: "form_submission",
        sourceId: crypto.randomUUID(),
      });

      const otherLead2 = await leadFactory.create({
        propertyId: property.id,
        highlevelContactId: null,
      });

      // Mock HighLevel client response
      const expectedContactId = "hl-contact-123";
      mockHighLevelClient.upsertContact.mockResolvedValue(
        ok({
          contactId: expectedContactId,
          responseBody: {
            contact: {
              id: expectedContactId,
            },
          },
        }),
      );

      // Execute test
      const result = await service.createContactInHighLevelIfNotExists({
        leadId: testLead.id,
      });

      // Verify success
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.highLevelContactId).toBe(expectedContactId);
      }

      // Verify HighLevel client was called correctly
      expect(mockHighLevelClient.upsertContact).toHaveBeenCalledWith({
        lead: expect.objectContaining({
          id: testLead.id,
          email: "<EMAIL>",
          firstName: "Test",
          lastName: "User",
        }),
        authToken: "test-token",
        locationId: "test-location",
      });

      // Verify ONLY the target lead was updated in database
      const updatedTestLead = await db.query.lead.findFirst({
        where: (fields, { eq }) => eq(fields.id, testLead.id),
      });
      expect(updatedTestLead?.highlevelContactId).toBe(expectedContactId);

      // Verify other leads were NOT updated
      const updatedOtherLead1 = await db.query.lead.findFirst({
        where: (fields, { eq }) => eq(fields.id, otherLead1.id),
      });
      expect(updatedOtherLead1?.highlevelContactId).toBeNull();

      const updatedOtherLead2 = await db.query.lead.findFirst({
        where: (fields, { eq }) => eq(fields.id, otherLead2.id),
      });
      expect(updatedOtherLead2?.highlevelContactId).toBeNull();
    });

    it("returns existing contact ID if lead already has one", async () => {
      // Create test property
      const property = await propertyFactory.create({
        leadAssignmentIntegrationToken: "test-token",
        leadAssignmentIntegrationLocationId: "test-location",
      });

      const existingContactId = "existing-hl-contact-123";
      // Create test lead with existing Highlevel contact ID
      const testLead = await leadFactory.create({
        propertyId: property.id,
        highlevelContactId: existingContactId,
        source: "form_submission",
        sourceId: crypto.randomUUID(),
      });

      // Execute test
      const result = await service.createContactInHighLevelIfNotExists({
        leadId: testLead.id,
      });

      // Verify success and existing contact ID is returned
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.highLevelContactId).toBe(existingContactId);
      }

      // Verify HighLevel client was not called
      expect(mockHighLevelClient.upsertContact).not.toHaveBeenCalled();
    });

    it("handles non-existent lead", async () => {
      const nonExistentLeadId = crypto.randomUUID();

      // Execute test and expect error
      const result = await service.createContactInHighLevelIfNotExists({
        leadId: nonExistentLeadId,
      });
      expect(result.isErr(), `result: ${JSON.stringify(result)}`).toEqual(true);

      // Verify HighLevel client was not called
      expect(mockHighLevelClient.upsertContact).not.toHaveBeenCalled();
    });

    it("handles HighLevel API error", async () => {
      // Create test property
      const property = await propertyFactory.create({
        leadAssignmentIntegrationToken: "test-token",
        leadAssignmentIntegrationLocationId: "test-location",
      });

      // Create test lead
      const testLead = await leadFactory.create({ propertyId: property.id });

      // Mock HighLevel client error
      const errorMessage = "HighLevel API error";
      mockHighLevelClient.upsertContact.mockResolvedValue(
        err({
          type: "api_error",
          message: errorMessage,
          status: 400,
          details: {},
        }),
      );

      // Execute test
      const result = await service.createContactInHighLevelIfNotExists({
        leadId: testLead.id,
      });

      // Verify error
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.message).toBe(errorMessage);
      }

      // Verify lead was not updated in database
      const updatedLead = await db.query.lead.findFirst({
        where: (fields, { eq }) => eq(fields.id, testLead.id),
      });
      expect(updatedLead?.highlevelContactId).toBeNull();
    });
  });

  describe("creating opportunities in high level", () => {
    it("successfully syncs a lead opportunity to HighLevel and only updates that specific lead", async () => {
      // Create test property
      const property = await propertyFactory.create({
        leadAssignmentIntegrationToken: "test-token",
        leadAssignmentIntegrationLocationId: "test-location",
        leadAssignmentIntegrationPipelineId: "test-pipeline",
      });

      // Create multiple test leads to ensure isolation
      const otherLead1 = await leadFactory.create({
        propertyId: property.id,
        highlevelContactId: "hl-contact-other-1",
        highlevelOpportunityId: null,
      });

      // Create test lead with HighLevel contact ID and a specific creation date
      const testLead = await leadFactory.create({
        propertyId: property.id,
        email: "<EMAIL>",
        firstName: "Test",
        lastName: "User",
        highlevelContactId: "hl-contact-123",
        source: "form_submission",
        sourceId: crypto.randomUUID(),
        createdAt: new Date(2023, 0, 2, 12, 0, 0), // January 2nd (even day)
      });

      const otherLead2 = await leadFactory.create({
        propertyId: property.id,
        highlevelContactId: "hl-contact-other-2",
        highlevelOpportunityId: null,
      });

      // Mock HighLevel client response
      const expectedOpportunityId = "hl-opp-123";
      mockHighLevelClient.createOpportunity.mockResolvedValue(
        ok({
          opportunityId: expectedOpportunityId,
          responseBody: {
            opportunity: {
              id: expectedOpportunityId,
            },
          },
        }),
      );

      // Execute test
      const result = await service.createOpportunityInHighlevelIfNotExists({
        leadId: testLead.id,
      });

      // Verify success
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.highLevelOpportunityId).toBe(expectedOpportunityId);
        expect(result.value.highLevelAuthToken).toBe("test-token");
        expect(result.value.highLevelLocationId).toBe("test-location");
      }

      // Verify HighLevel client was called correctly
      expect(mockHighLevelClient.createOpportunity).toHaveBeenCalledWith(
        expect.objectContaining({
          contactId: "hl-contact-123",
          pipelineId: "test-pipeline",
          authToken: "test-token",
          locationId: "test-location",
          evenOddAssignmentDate: "even",
          formId: null, // This test uses form_submission source but no actual form submission exists
          lead: expect.objectContaining({
            id: testLead.id,
          }),
        }),
      );

      // Verify ONLY the target lead was updated in database
      const updatedTestLead = await db.query.lead.findFirst({
        where: (fields, { eq }) => eq(fields.id, testLead.id),
      });
      expect(updatedTestLead?.highlevelOpportunityId).toBe(
        expectedOpportunityId,
      );

      // Verify other leads were NOT updated
      const updatedOtherLead1 = await db.query.lead.findFirst({
        where: (fields, { eq }) => eq(fields.id, otherLead1.id),
      });
      expect(updatedOtherLead1?.highlevelOpportunityId).toBeNull();

      const updatedOtherLead2 = await db.query.lead.findFirst({
        where: (fields, { eq }) => eq(fields.id, otherLead2.id),
      });
      expect(updatedOtherLead2?.highlevelOpportunityId).toBeNull();
    });

    it("returns existing opportunity ID and auth data if lead already has opportunity", async () => {
      // Create test property
      const property = await propertyFactory.create({
        leadAssignmentIntegrationToken: "test-token",
        leadAssignmentIntegrationLocationId: "test-location",
        leadAssignmentIntegrationPipelineId: "test-pipeline",
      });

      const existingOpportunityId = "existing-hl-opportunity-123";
      // Create test lead with existing Highlevel opportunity ID
      const testLead = await leadFactory.create({
        propertyId: property.id,
        highlevelContactId: "hl-contact-123",
        highlevelOpportunityId: existingOpportunityId,
        source: "form_submission",
        sourceId: crypto.randomUUID(),
      });

      // Execute test
      const result = await service.createOpportunityInHighlevelIfNotExists({
        leadId: testLead.id,
      });

      // Verify success and all required data is returned
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.highLevelOpportunityId).toBe(existingOpportunityId);
        expect(result.value.highLevelAuthToken).toBe("test-token");
        expect(result.value.highLevelLocationId).toBe("test-location");
      }

      // Verify HighLevel client was not called
      expect(mockHighLevelClient.createOpportunity).not.toHaveBeenCalled();
    });

    it("fails if lead has no HighLevel contact ID", async () => {
      // Create test property
      const property = await propertyFactory.create({
        leadAssignmentIntegrationToken: "test-token",
        leadAssignmentIntegrationLocationId: "test-location",
      });

      // Create test lead without HighLevel contact ID
      const testLead = await leadFactory.create({
        propertyId: property.id,
        highlevelContactId: null,
      });

      // Execute test
      const result = await service.createOpportunityInHighlevelIfNotExists({
        leadId: testLead.id,
      });

      // Verify error
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.message).toContain(
          "is missing the highlevel contactId",
        );
      }

      // Verify HighLevel client was not called
      expect(mockHighLevelClient.createOpportunity).not.toHaveBeenCalled();
    });

    it("handles non-existent lead", async () => {
      const nonExistentLeadId = crypto.randomUUID();

      // Execute test
      const result = await service.createOpportunityInHighlevelIfNotExists({
        leadId: nonExistentLeadId,
      });

      // Verify error
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.message).toContain("not found in database");
      }

      // Verify HighLevel client was not called
      expect(mockHighLevelClient.createOpportunity).not.toHaveBeenCalled();
    });

    it("handles HighLevel API error", async () => {
      // Create test property
      const property = await propertyFactory.create({
        leadAssignmentIntegrationToken: "test-token",
        leadAssignmentIntegrationLocationId: "test-location",
      });

      // Create test lead
      const testLead = await leadFactory.create({
        propertyId: property.id,
        highlevelContactId: "hl-contact-123",
      });

      // Mock HighLevel client error
      const errorMessage = "HighLevel API error";
      mockHighLevelClient.createOpportunity.mockResolvedValue(
        err({
          type: "api_error",
          message: errorMessage,
          status: 400,
          details: {},
        }),
      );

      // Execute test
      const result = await service.createOpportunityInHighlevelIfNotExists({
        leadId: testLead.id,
      });

      // Verify error
      expect(result.isErr()).toBe(true);
      if (result.isErr()) {
        expect(result.error.message).toBe(errorMessage);
      }

      // Verify lead was not updated in database
      const updatedLead = await db.query.lead.findFirst({
        where: (fields, { eq }) => eq(fields.id, testLead.id),
      });
      expect(updatedLead?.highlevelOpportunityId).toBeNull();
    });

    it("includes even/odd assignment date when creating opportunity", async () => {
      // Create test property
      const property = await propertyFactory.create({
        leadAssignmentIntegrationToken: "test-token",
        leadAssignmentIntegrationLocationId: "test-location",
        leadAssignmentIntegrationPipelineId: "test-pipeline",
      });

      // Create test lead with HighLevel contact ID and a specific creation date (January 2, 2023 - even day)
      const testLead = await leadFactory.create({
        propertyId: property.id,
        email: "<EMAIL>",
        firstName: "Test",
        lastName: "User",
        highlevelContactId: "hl-contact-123",
        source: "form_submission",
        sourceId: crypto.randomUUID(),
        createdAt: new Date(2023, 0, 2, 12, 0, 0), // January 2nd (even day)
      });

      // Mock HighLevel client response
      const expectedOpportunityId = "hl-opp-123";
      mockHighLevelClient.createOpportunity.mockResolvedValue(
        ok({
          opportunityId: expectedOpportunityId,
          responseBody: {
            opportunity: {
              id: expectedOpportunityId,
            },
          },
        }),
      );

      // Execute test
      const result = await service.createOpportunityInHighlevelIfNotExists({
        leadId: testLead.id,
      });

      // Verify success
      expect(result.isOk()).toBe(true);
      if (result.isOk()) {
        expect(result.value.highLevelOpportunityId).toBe(expectedOpportunityId);
        expect(result.value.highLevelAuthToken).toBe("test-token");
        expect(result.value.highLevelLocationId).toBe("test-location");
      }

      // Verify the opportunity was created with the correct parameters
      expect(mockHighLevelClient.createOpportunity).toHaveBeenCalledWith(
        expect.objectContaining({
          contactId: "hl-contact-123",
          pipelineId: "test-pipeline",
          authToken: "test-token",
          locationId: "test-location",
          evenOddAssignmentDate: "even",
          lead: expect.objectContaining({
            id: testLead.id,
          }),
        }),
      );
    });

    it("includes form_id when lead is from form submission", async () => {
      // Create test property
      const property = await propertyFactory.create({
        leadAssignmentIntegrationToken: "test-token",
        leadAssignmentIntegrationLocationId: "test-location",
        leadAssignmentIntegrationPipelineId: "test-pipeline",
      });

      // Create test form
      const form = await formFactory.create({
        propertyId: property.id,
      });

      // Create form submission
      const submissionId = crypto.randomUUID();
      await db.insert(formSubmission).values({
        id: submissionId,
        formId: form.id,
        data: {},
        metadata: {},
      });

      // Create test lead with form submission source
      const testLead = await leadFactory.create({
        propertyId: property.id,
        highlevelContactId: "hl-contact-123",
        source: "form_submission",
        sourceId: submissionId,
      });

      // Mock HighLevel client response
      const expectedOpportunityId = "hl-opp-123";
      mockHighLevelClient.createOpportunity.mockResolvedValue(
        ok({
          opportunityId: expectedOpportunityId,
          responseBody: {
            opportunity: {
              id: expectedOpportunityId,
            },
          },
        }),
      );

      // Execute test
      const result = await service.createOpportunityInHighlevelIfNotExists({
        leadId: testLead.id,
      });

      // Verify success
      expect(result.isOk()).toBe(true);

      // Verify HighLevel client was called with formId parameter
      expect(mockHighLevelClient.createOpportunity).toHaveBeenCalledWith(
        expect.objectContaining({
          formId: form.id,
          lead: expect.objectContaining({
            id: testLead.id,
          }),
        }),
      );
    });

    it("does not include form_id when lead is from API source", async () => {
      // Create test property
      const property = await propertyFactory.create({
        leadAssignmentIntegrationToken: "test-token",
        leadAssignmentIntegrationLocationId: "test-location",
        leadAssignmentIntegrationPipelineId: "test-pipeline",
      });

      // Create test lead with API source
      const testLead = await leadFactory.create({
        propertyId: property.id,
        highlevelContactId: "hl-contact-123",
        source: "api",
        sourceId: crypto.randomUUID(),
      });

      // Mock HighLevel client response
      const expectedOpportunityId = "hl-opp-123";
      mockHighLevelClient.createOpportunity.mockResolvedValue(
        ok({
          opportunityId: expectedOpportunityId,
          responseBody: {
            opportunity: {
              id: expectedOpportunityId,
            },
          },
        }),
      );

      // Execute test
      const result = await service.createOpportunityInHighlevelIfNotExists({
        leadId: testLead.id,
      });

      // Verify success
      expect(result.isOk()).toBe(true);

      // Verify HighLevel client was called with null formId parameter
      expect(mockHighLevelClient.createOpportunity).toHaveBeenCalledWith(
        expect.objectContaining({
          formId: null,
          lead: expect.objectContaining({
            id: testLead.id,
          }),
        }),
      );
    });

    it("handles missing form submission gracefully", async () => {
      // Create test property
      const property = await propertyFactory.create({
        leadAssignmentIntegrationToken: "test-token",
        leadAssignmentIntegrationLocationId: "test-location",
        leadAssignmentIntegrationPipelineId: "test-pipeline",
      });

      // Create test lead with form_submission source but non-existent sourceId
      const testLead = await leadFactory.create({
        propertyId: property.id,
        highlevelContactId: "hl-contact-123",
        source: "form_submission",
        sourceId: crypto.randomUUID(), // This ID doesn't exist in form_submission table
      });

      // Mock HighLevel client response
      const expectedOpportunityId = "hl-opp-123";
      mockHighLevelClient.createOpportunity.mockResolvedValue(
        ok({
          opportunityId: expectedOpportunityId,
          responseBody: {
            opportunity: {
              id: expectedOpportunityId,
            },
          },
        }),
      );

      // Execute test
      const result = await service.createOpportunityInHighlevelIfNotExists({
        leadId: testLead.id,
      });

      // Verify success
      expect(result.isOk()).toBe(true);

      // Verify HighLevel client was called with null formId parameter
      expect(mockHighLevelClient.createOpportunity).toHaveBeenCalledWith(
        expect.objectContaining({
          formId: null,
          lead: expect.objectContaining({
            id: testLead.id,
          }),
        }),
      );
    });
  });
});
