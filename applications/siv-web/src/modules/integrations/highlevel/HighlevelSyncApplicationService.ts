import { HighLevelClient } from "@siv/highlevel-client";
import { eq, InferSelectModel } from "drizzle-orm";
import { lead, property, formSubmission } from "@/drizzle/schema";
import { db } from "@/db/dbclient";
import logger from "@/logger";
import { toDomain } from "@/modules/leads/infrastructure/lead-db-data-mapper";
import { err, ok, Result } from "neverthrow";
import { determineEvenOddAssignmentDate } from "@/modules/integrations/highlevel/lead-assignment/even-odd-day-assignment";

type SyncError = {
  message: string;
};

export class HighlevelSyncApplicationService {
  constructor(private readonly highLevelClient: HighLevelClient) {}

  async createContactInHighLevelIfNotExists(params: {
    leadId: string;
  }): Promise<
    Result<
      {
        highLevelContactId: string;
      },
      SyncError
    >
  > {
    const leadRecord: InferSelectModel<typeof lead>[] = await db
      .select()
      .from(lead)
      .where(eq(lead.id, params.leadId));
    if (leadRecord.length === 0) {
      const message = `Lead with id ${params.leadId} not found in database`;
      logger.error(message);
      return err({ message });
    }

    const leadDomainObj = toDomain(leadRecord[0]);

    if (leadDomainObj.highlevelContactId) {
      logger.info(
        `Lead ${params.leadId} already has Highlevel contact ID ${leadDomainObj.highlevelContactId}`,
      );
      return ok({ highLevelContactId: leadDomainObj.highlevelContactId });
    }

    const propertyRecord = await db
      .select()
      .from(property)
      .where(eq(property.id, leadDomainObj.propertyId));
    if (propertyRecord.length === 0) {
      const message = `property with id ${leadDomainObj.propertyId} not found in database`;
      logger.error(message);
      return err({ message });
    }
    const result = await this.highLevelClient.upsertContact({
      lead: leadDomainObj,
      authToken: propertyRecord[0].leadAssignmentIntegrationToken!!,
      locationId: propertyRecord[0].leadAssignmentIntegrationLocationId!!,
    });

    return result
      .asyncMap(async (result) => {
        await db
          .update(lead)
          .set({
            highlevelContactId: result.contactId,
          })
          .where(eq(lead.id, params.leadId));

        return { highLevelContactId: result.contactId };
      })
      .mapErr((error) => {
        logger.error("Failed to sync contact to HighLevel", {
          leadId: lead.id,
          error: error.message,
        });
        return { message: error.message };
      });
  }

  async createOpportunityInHighlevelIfNotExists(params: {
    leadId: string;
  }): Promise<
    Result<
      {
        highLevelOpportunityId: string;
        highLevelAuthToken: string;
        highLevelLocationId: string;
      },
      SyncError
    >
  > {
    const leadRecord: InferSelectModel<typeof lead>[] = await db
      .select()
      .from(lead)
      .where(eq(lead.id, params.leadId));
    if (leadRecord.length === 0) {
      const message = `Lead with id ${params.leadId} not found in database`;
      logger.error(message);
      return err({ message });
    }
    const leadDomainObj = toDomain(leadRecord[0]);

    const propertyRecord = await db
      .select()
      .from(property)
      .where(eq(property.id, leadDomainObj.propertyId));
    if (propertyRecord.length === 0) {
      const message = `property with id ${leadDomainObj.propertyId} not found in database`;
      logger.error(message);
      return err({ message });
    }

    const authToken = propertyRecord[0].leadAssignmentIntegrationToken!!;
    const locationId = propertyRecord[0].leadAssignmentIntegrationLocationId!!;
    const pipelineId = propertyRecord[0].leadAssignmentIntegrationPipelineId!!;

    if (leadDomainObj.highlevelOpportunityId) {
      logger.info(
        `Lead ${params.leadId} already has Highlevel opportunity ID ${leadDomainObj.highlevelOpportunityId} - skipping create opportunity call to GHL`,
      );
      return ok({
        highLevelOpportunityId: leadDomainObj.highlevelOpportunityId,
        highLevelAuthToken: authToken,
        highLevelLocationId: locationId,
      });
    }

    if (leadDomainObj.highlevelContactId === null) {
      return err({
        message: `Lead with id=${leadDomainObj.id} is missing the highlevel contactId`,
      });
    }

    // Determine even/odd assignment based on the lead's creation date
    const evenOddAssignmentDate = determineEvenOddAssignmentDate(leadDomainObj);

    // If lead source is form_submission, fetch the formId
    let formId: string | undefined;
    if (
      leadDomainObj.source &&
      leadDomainObj.source === "form_submission" &&
      leadDomainObj.sourceId
    ) {
      const formSubmissionRecord = await db
        .select()
        .from(formSubmission)
        .where(eq(formSubmission.id, leadDomainObj.sourceId))
        .limit(1);

      if (formSubmissionRecord.length > 0) {
        formId = formSubmissionRecord[0].formId;
      }
    }

    const result = await this.highLevelClient.createOpportunity({
      lead: leadDomainObj,
      contactId: leadDomainObj.highlevelContactId!,
      pipelineId: pipelineId,
      authToken: authToken,
      locationId: locationId,
      evenOddAssignmentDate: evenOddAssignmentDate,
      formId: formId || null,
    });

    return result
      .asyncMap(async (result) => {
        await db
          .update(lead)
          .set({
            highlevelOpportunityId: result.opportunityId,
          })
          .where(eq(lead.id, params.leadId));

        return {
          highLevelOpportunityId: result.opportunityId,
          highLevelAuthToken: authToken,
          highLevelLocationId: locationId,
        };
      })
      .mapErr((error) => {
        logger.error("Failed to sync opportunity to HighLevel", {
          leadId: params.leadId,
          error: error.message,
        });
        return { message: error.message };
      });
  }
}
