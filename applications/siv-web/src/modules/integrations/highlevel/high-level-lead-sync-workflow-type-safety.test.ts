import { describe, it, expectTypeOf } from "vitest";
import { LeadCreatedEvent } from "@/modules/leads/domain/events";

describe("HighLevel Lead Sync Workflow Type Safety", () => {
  it("should ensure LeadCreatedEvent has propertyId at compile time", () => {
    // This test ensures that LeadCreatedEvent data contains propertyId
    // If propertyId is removed from the event type, this test will fail to compile
    type EventData = LeadCreatedEvent["data"];

    // These type assertions will fail to compile if propertyId is removed
    expectTypeOf<EventData>().toHaveProperty("propertyId");
    expectTypeOf<EventData["propertyId"]>().toBeString();

    // Ensure the event data has the expected shape
    const testEvent: LeadCreatedEvent = {
      name: "lead.created",
      id: "test-id",
      data: {
        leadId: "test-lead-id",
        propertyId: "test-property-id", // This line will error if propertyId is removed from the type
      },
    };

    // Access propertyId to ensure it exists (will fail to compile if removed)
    const propertyId: string = testEvent.data.propertyId;
    expectTypeOf(propertyId).toBeString();
  });

  it("should enforce propertyId exists in concurrency key path", () => {
    // This simulates what the createConcurrencyKey function does
    type ConcurrencyKeyPath<T extends LeadCreatedEvent> = T["data"] extends {
      propertyId: string;
    }
      ? "event.data.propertyId"
      : never;

    // This will be "event.data.propertyId" if propertyId exists, or never if it doesn't
    type ActualPath = ConcurrencyKeyPath<LeadCreatedEvent>;

    // This assertion ensures the path is valid (not never)
    const path: ActualPath = "event.data.propertyId";
    expectTypeOf(path).toEqualTypeOf<"event.data.propertyId">();
  });
});
