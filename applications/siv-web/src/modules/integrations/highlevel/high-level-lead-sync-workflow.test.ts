import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import { InngestTestEngine } from "@inngest/test";
import { mock, MockProxy } from "vitest-mock-extended";
import { err, ok } from "neverthrow";
import { createHighLevelLeadSyncWorkflow } from "./high-level-lead-sync-workflow";
import { HighlevelSyncApplicationService } from "./HighlevelSyncApplicationService";
import { createInngestClient } from "@/inngest-client";
import { withTransactionalTests } from "@/test/transaction-test-helpers";
import {
  leadFactory,
  leadFileFactory,
} from "@/modules/leads/lead-test-factories";

describe("HighLevel Lead Sync Workflow", () => {
  withTransactionalTests();

  let highlevelSyncService: MockProxy<HighlevelSyncApplicationService>;
  let testEngine: InngestTestEngine;
  let leadId: string;

  const contactId = "test-contact-456";
  const opportunityId = "test-opportunity-789";

  beforeEach(async () => {
    highlevelSyncService = mock<HighlevelSyncApplicationService>();
    leadId = (await (await leadFactory.withCreatedProperty()).create()).id;
    const inngest = createInngestClient();

    const fn = createHighLevelLeadSyncWorkflow(inngest, highlevelSyncService);
    testEngine = new InngestTestEngine({
      function: fn,
    });
  });

  function givenHighlevelSyncSucceeds() {
    highlevelSyncService.createContactInHighLevelIfNotExists.mockResolvedValue(
      ok({ highLevelContactId: contactId }),
    );

    highlevelSyncService.createOpportunityInHighlevelIfNotExists.mockResolvedValue(
      ok({
        highLevelOpportunityId: opportunityId,
        highLevelAuthToken: "test-auth-token",
        highLevelLocationId: "test-location-id",
      }),
    );
  }

  it("successfully syncs a lead to HighLevel with both contact and opportunity", async () => {
    givenHighlevelSyncSucceeds();

    const { result, state } = await testEngine.execute({
      events: [
        {
          name: "lead.created",
          data: { leadId: leadId, propertyId: "test-property-id" },
        },
      ],
    });

    expect(result).toEqual({ success: true });

    expect(await state["sync-contact-to-highlevel"]).toEqual({
      highLevelContactId: contactId,
    });

    expect(await state["sync-to-highlevel-opportunity"]).toEqual({
      highLevelOpportunityId: opportunityId,
      highLevelAuthToken: "test-auth-token",
      highLevelLocationId: "test-location-id",
    });

    expect(
      highlevelSyncService.createContactInHighLevelIfNotExists,
    ).toHaveBeenCalledWith({
      leadId: leadId,
    });

    expect(
      highlevelSyncService.createOpportunityInHighlevelIfNotExists,
    ).toHaveBeenCalledWith({
      leadId: leadId,
    });
  });

  it("emits lead.synced-to-highlevel event after successful sync", async () => {
    const file = await leadFileFactory.create({ leadId: leadId });
    givenHighlevelSyncSucceeds();

    const mockSendEvent = vi.fn().mockResolvedValue(undefined);

    const { state, ctx } = await testEngine.execute({
      events: [
        {
          name: "lead.created",
          data: { leadId: leadId, propertyId: "test-property-id" },
        },
      ],
      steps: [
        {
          id: "emit-lead-synced-event",
          handler: mockSendEvent,
        },
      ],
    });

    expect(mockSendEvent).toHaveBeenCalled();

    expect(ctx.step.sendEvent).toHaveBeenCalledWith(
      "emit-lead-synced-event",
      expect.objectContaining({
        name: "lead.synced-to-highlevel",
        data: expect.objectContaining({
          leadId: leadId,
          highLevelContactId: contactId,
          highLevelOpportunityId: opportunityId,
          highLevelAuthToken: expect.any(String),
          highLevelLocationId: expect.any(String),
          fileUploads: expect.arrayContaining([
            expect.objectContaining({
              contentType: file.contentType,
              fileName: file.fileName,
              s3Key: file.s3Key,
              keyWithoutStatusPrefix: file.s3KeyWithoutStatusPrefix,
            }),
          ]),
        }),
      }),
    );
  });

  it("handles errors when opportunity sync fails", async () => {
    highlevelSyncService.createContactInHighLevelIfNotExists.mockResolvedValueOnce(
      ok({ highLevelContactId: contactId }),
    );
    highlevelSyncService.createOpportunityInHighlevelIfNotExists.mockResolvedValueOnce(
      err({ message: "Failed to sync opportunity" }),
    );

    const { error } = await testEngine.execute({
      events: [
        {
          name: "lead.created",
          data: { leadId: leadId, propertyId: "test-property-id" },
        },
      ],
    });

    expect(
      highlevelSyncService.createContactInHighLevelIfNotExists,
    ).toHaveBeenCalledWith({
      leadId: leadId,
    });

    expect(
      highlevelSyncService.createOpportunityInHighlevelIfNotExists,
    ).toHaveBeenCalledWith({
      leadId: leadId,
    });

    expect(error).toEqual(
      expect.objectContaining({
        message:
          "Failing highlevel contact sync step: Failed to sync opportunity",
      }),
    );
  });
});
